"""
Custom exceptions for BankOne API operations.
"""
from rest_framework.exceptions import APIException


class BankOneAPIError(Exception):
    """Base exception for BankOne API errors."""
    pass


class BankOneAuthenticationError(BankOneAPIError):
    """Raised when authentication fails."""
    pass


class BankOneValidationError(BankOneAPIError):
    """Raised when request validation fails."""
    pass


class BankOneNetworkError(BankOneAPIError):
    """Raised when network/connection issues occur."""
    pass


class BankOneTransactionError(BankOneAPIError):
    """Raised when transaction operations fail."""
    pass


class BankOneAccountError(BankOneAPIError):
    """Raised when account operations fail."""
    pass


class BankOneCustomerError(BankOneAPIError):
    """Raised when customer operations fail."""
    pass


class InvalidRequestException(APIException):
    status_code = 400
    default_detail = 'Invalid request'
    default_code = 'invalid_request'


def raise_serializer_error_msg(errors: {}):
    error_message = ""
    for err_key, err_val in errors.items():
        if type(err_val) is list:
            err_msg = ', '.join(err_val)
            error_message = f'Error occurred on \'{err_key.replace("_", " ")}\' field: {err_msg}'
        else:
            for err_val_key, err_val_val in err_val.items():
                err_msg = ', '.join(err_val_val)
                error_message = f'Error occurred on \'{err_val_key}\' field: {err_msg}'
        raise InvalidRequestException(error_message)


def create_error_message(key, values):
    data = dict()
    data[key] = str(values).split('|')
    raise InvalidRequestException(data)


class MaximumApprovalPINRetriesException(APIException):
    status_code = 401
    default_detail = {"detail": "Oops! You have entered incorrect transaction PIN too many times. Please contact bank administrator for support"}
    default_code = "Not permitted"


class NoCompanyAccountException(APIException):
    status_code = 401
    default_detail = {"detail": "Company account not found"}
    default_code = "Not permitted"



