import base64
import calendar
import datetime
import decimal
import json
import logging
import uuid

import requests
from django.conf import settings

from dateutil.relativedelta import relativedelta
from cryptography.fernet import Fernet
from django.db.models.aggregates import Sum

from bowenmfb.modules.exceptions import InvalidRequestException


def log_request(*args):
    for arg in args:
        logging.info(arg)


def format_phone_number(phone_number):
    phone_number = f"0{phone_number[-10:]}"
    return phone_number


def encrypt_text(text: str):
    key = base64.urlsafe_b64encode(settings.SECRET_KEY.encode()[:32])
    fernet = Fernet(key)
    secure = fernet.encrypt(f"{text}".encode())
    return secure.decode()


def decrypt_text(text: str):
    key = base64.urlsafe_b64encode(settings.SECRET_KEY.encode()[:32])
    fernet = Fernet(key)
    decrypt = fernet.decrypt(text.encode())
    return decrypt.decode()


def generate_transaction_reference():
    # Provider's length is 12 characters
    from transfer.models import SingleTransfer, BulkTransfer
    from datetime import datetime

    now = datetime.now()
    year = str(now.year)[2:]
    month = str(now.month)
    if len(month) == 1:
        month = f"0{month}"
    unique_identifier = str(uuid.uuid4().int)[:7]

    while True:
        ref_code = f"B{year}{month}{unique_identifier}"
        print(ref_code)
        if not (SingleTransfer.objects.filter(reference=ref_code).exists() or
                BulkTransfer.objects.filter(reference=ref_code).exists()):
            return ref_code


def generate_random_otp(count: int):
    return str(uuid.uuid4().int)[:count]


def get_previous_date(date, delta):
    previous_date = date - datetime.timedelta(days=delta)
    return previous_date


def get_next_date(date, delta):
    next_date = date + datetime.timedelta(days=delta)
    return next_date


def get_next_weekday(date, weekday):
    days_ahead = weekday - date.weekday()
    if days_ahead <= 0:
        days_ahead += 7
    return date + datetime.timedelta(days_ahead)


def get_week_start_and_end_datetime(date_time):
    week_start = date_time - datetime.timedelta(days=date_time.weekday())
    week_end = week_start + datetime.timedelta(days=6)
    week_start = datetime.datetime.combine(week_start.date(), datetime.time.min)
    week_end = datetime.datetime.combine(week_end.date(), datetime.time.max)
    return week_start, week_end


def get_month_start_and_end_datetime(date_time):
    month_start = date_time.replace(day=1)
    month_end = month_start.replace(day=calendar.monthrange(month_start.year, month_start.month)[1])
    month_start = datetime.datetime.combine(month_start.date(), datetime.time.min)
    month_end = datetime.datetime.combine(month_end.date(), datetime.time.max)
    return month_start, month_end


def get_year_start_and_end_datetime(date_time):
    year_start = date_time.replace(day=1, month=1, year=date_time.year)
    year_end = date_time.replace(day=31, month=12, year=date_time.year)
    year_start = datetime.datetime.combine(year_start.date(), datetime.time.min)
    year_end = datetime.datetime.combine(year_end.date(), datetime.time.max)
    return year_start, year_end


def get_previous_month_date(date, delta):
    return date - relativedelta(months=delta)


def mask_number(number):
    first_covered = number[0:3]
    second_covered = number[-3:]
    total_covered = first_covered + "******" + second_covered
    return total_covered


def get_next_minute(date, delta):
    next_minute = date + relativedelta(minutes=delta)
    return next_minute


def get_previous_minute(date, delta):
    previous_minute = date - relativedelta(minutes=delta)
    return previous_minute


def get_previous_seconds(date, delta):
    previous_seconds = date - relativedelta(seconds=delta)
    return previous_seconds


def get_previous_hour(date, delta):
    previous_hour = date - relativedelta(hours=delta)
    return previous_hour


def get_account_balance(account_number):
    from bowenmfb.modules.bankone import BankOneClient
    from account.models import CompanyAccount

    client = BankOneClient()
    try:
        account_id = CompanyAccount.objects.get(account_number=account_number).id
    except CompanyAccount.DoesNotExist:
        account_id = None

    available = 0.0
    ledger = 0.0
    withdraw_able = 0.0
    response = client.get_account_by_number(account_number)
    if "AvailableBalance" in response:
        available = decimal.Decimal(str(response["AvailableBalance"]).replace(",", ""))
    if "LedgerBalance" in response:
        ledger = decimal.Decimal(str(response["LedgerBalance"]).replace(",", ""))
    if "WithdrawableBalance" in response:
        withdraw_able = decimal.Decimal(str(response["WithdrawableBalance"]).replace(",", ""))

    balance_data = {
        "account_id": account_id,
        "account_number": mask_number(account_number),
        "balances": {
            "available_balance": available,
            "ledger_balance": ledger,
            "withdrawable_balance": withdraw_able
        }
    }

    return balance_data


def validate_bvn_with_phone_number(bvn_number, phone_number):
    from bowenmfb.modules.bankone import BankOneClient
    client = BankOneClient()

    response = client.get_bvn_details(bvn=bvn_number)

    if ("RequestStatus" in response and response["RequestStatus"] is True) and ("isBvnValid" in response and response["isBvnValid"] is True):
        bvn_phone_number = response["bvnDetails"]["phoneNumber"]
        if settings.DEBUG is True:
            bvn_phone_number = phone_number
        if str(phone_number).strip() == str(bvn_phone_number).strip():
            return True

    return False


def send_tmsaas_sms(content, receiver):
    baseUrl = settings.TMSAAS_BASE_URL
    header = {
        "client-id": settings.TMSAAS_CLIENT_ID,
        "Content-Type": "application/json"
    }
    url = f"{baseUrl}/sms"
    payload = json.dumps({"message": content, "provider": settings.TMSAAS_SENDER_ID, "recipients": [receiver]})
    log_request("POST", f"url: {url}", f"header: {header}", f"payload: {payload}")
    response = requests.request("POST", url=url, headers=header, data=payload)
    log_request(f"response: {response.text}")
    return response.json()


def send_tmsaas_email(content, receiver, subject):
    from account.models import BankConstantTable
    constant = BankConstantTable.objects.first()
    baseUrl = settings.TMSAAS_BASE_URL
    header = {
        "client-id": settings.TMSAAS_CLIENT_ID,
        "Content-Type": "application/json"
    }
    url = f"{baseUrl}/email"
    payload = json.dumps(
        {
            "from": str(constant.support_email) or "<EMAIL>",
            "type": "custom",
            "html": content,
            "subject": subject,
            "recipients": [
                receiver
            ]
        }
    )
    log_request("POST", f"url: {url}", f"header: {header}", f"payload: {payload}")
    response = requests.request("POST", url=url, headers=header, data=payload)
    log_request(f"response: {response.text}")
    return response.json()


def send_notification_async(
    notification_type: str,
    recipient: str,
    message: str,
    subject: str = None,
    account_number: str = None,
    reference_no: str = None,
):
    """
    Helper function to send notifications asynchronously using Celery.

    Args:
        notification_type: 'sms' or 'email'
        recipient: Phone number for SMS or email address for email
        message: Message content
        subject: Email subject (required for email)
        account_number: Account number for SMS (optional, will use default if not provided)
        reference_no: Reference number for SMS (optional, will generate if not provided)
        institution_code: Institution code for email (has default)
        mfb_code: MFB code for email (has default)
        email_from: From email address (has default)

    Returns:
        AsyncResult: Celery task result
    """
    from superadmin.tasks import send_notification_task
    from account.models import BankConstantTable

    # Generate reference number if not provided for SMS
    if notification_type == 'sms' and not reference_no:
        reference_no = f"BowenSMS{generate_random_otp(6)}"

    # Use default account number if not provided for SMS
    if notification_type == 'sms' and not account_number:
        # You might want to get this from settings or database
        account_number = getattr(settings, 'DEFAULT_SMS_ACCOUNT_NUMBER', '**********')

    bank_config = BankConstantTable.objects.first()
    institution_code = bank_config.institution_code
    mfb_code = bank_config.mfb_code
    email_from = bank_config.support_email

    return send_notification_task.delay(
        notification_type=notification_type,
        recipient=recipient,
        message=message,
        subject=subject,
        account_number=account_number,
        reference_no=reference_no,
        institution_code=institution_code,
        mfb_code=mfb_code,
        email_from=email_from
    )


def validate_customer_with_info_on_bankone(bvn, email, phone_number):
    """
    Validate customer data with BankOne API before registration.

    Args:
        bvn: Bank Verification Number
        email: Email address
        phone_number: Phone number

    Returns:
        dict: Validation results with detailed information

    Example:
        results = validate_customer_with_bankone("***********", "<EMAIL>", "***********")
        if results["can_proceed"]:
            # Proceed with registration
            pass
        else:
            # Handle conflicts
            for error in results["errors"]:
                print(error)
    """
    from bowenmfb.modules.bankone import BankOneClient, BankOneAPIError

    try:
        client = BankOneClient()

        # Initialize results
        validation_results = {
            "can_proceed": True,
            "errors": [],
            "warnings": [],
            "existing_customer_id": None,
            "details": {
                "bvn_available": True,
                "email_available": True,
                "phone_available": True
            }
        }

        # Check BVN
        try:
            bvn_response = client.check_bvn_exists(bvn)
            if bvn_response.get("IsSuccessful"):
                validation_results["can_proceed"] = False
                validation_results["details"]["bvn_available"] = False
                validation_results["existing_customer_id"] = bvn_response.get("CustomerIDInString")
                validation_results["errors"].append(f"BVN {bvn} is already registered in the system")
                validation_results["warnings"].append(f"Existing customer ID: {validation_results['existing_customer_id']}")
        except Exception as e:
            log_request(f"BVN validation error: {str(e)}")
            validation_results["can_proceed"] = False
            validation_results["warnings"].append("Could not validate BVN - proceeding with caution")

        # Check Email
        try:
            if client.check_email_exists(email):
                validation_results["can_proceed"] = False
                validation_results["details"]["email_available"] = False
                validation_results["errors"].append(f"Email {email} is already registered in the system")
        except Exception as e:
            log_request(f"Email validation error: {str(e)}")
            validation_results["warnings"].append("Could not validate email - proceeding with caution")

        # Check Phone
        try:
            if client.check_phone_exists(phone_number):
                validation_results["can_proceed"] = False
                validation_results["details"]["phone_available"] = False
                validation_results["errors"].append(f"Phone number {phone_number} is already registered in the system")
        except Exception as e:
            log_request(f"Phone validation error: {str(e)}")
            validation_results["warnings"].append("Could not validate phone - proceeding with caution")

        return validation_results

    except BankOneAPIError as e:
        log_request(f"BankOne API error during validation: {str(e)}")
        return {
            "can_proceed": False,
            "errors": ["Unable to validate customer data with BankOne API"],
            "warnings": [str(e)],
            "existing_customer_id": None,
            "details": {
                "bvn_available": None,
                "email_available": None,
                "phone_available": None
            }
        }
    except Exception as e:
        log_request(f"Unexpected error during customer validation: {str(e)}")
        return {
            "can_proceed": False,
            "errors": ["Unexpected error during validation"],
            "warnings": [str(e)],
            "existing_customer_id": None,
            "details": {
                "bvn_available": None,
                "email_available": None,
                "phone_available": None
            }
        }


def check_transaction_limit(account_id, amount):
    from account.models import CompanyAccount
    from transfer.models import SingleTransfer, BulkTransfer
    from billpayment.models import BillPayment

    account = CompanyAccount.objects.get(id=account_id)

    daily_limit = account.daily_transfer_limit
    monthly_limit = account.monthly_transfer_limit

    today = datetime.date.today()
    current_month = today.month

    # Check daily limit
    daily_single_transfers_amount = SingleTransfer.objects.filter(from_account=account,
        company=account.company, created_at__date=today, status="success").aggregate(Sum('amount'))['amount__sum'] or 0.0

    daily_bulk_transfers_amount = BulkTransfer.objects.filter(from_account=account,
        company=account.company, created_at__date=today, status="success").aggregate(Sum('amount'))['amount__sum'] or 0.0

    daily_bill_payments_amount = BillPayment.objects.filter(from_account=account,
        company=account.company, created_at__date=today, status="success").aggregate(Sum('amount'))['amount__sum'] or 0.0

    if daily_single_transfers_amount + daily_bulk_transfers_amount + daily_bill_payments_amount + amount > daily_limit:
        raise InvalidRequestException({"detail": "Daily transfer limit exceeded"})

    # Check monthly limit
    monthly_single_transfers_amount = SingleTransfer.objects.filter(from_account=account,
        company=account.company, created_at__month=current_month, status="success").aggregate(Sum('amount'))['amount__sum'] or 0.0

    monthly_bulk_transfers_amount = BulkTransfer.objects.filter(from_account=account,
        company=account.company, created_at__month=current_month, status="success").aggregate(Sum('amount'))['amount__sum'] or 0.0

    monthly_bill_payments_amount = BillPayment.objects.filter(from_account=account,
        company=account.company, created_at__month=current_month, status="success").aggregate(Sum('amount'))['amount__sum'] or 0.0

    if monthly_single_transfers_amount + monthly_bulk_transfers_amount + monthly_bill_payments_amount + amount > monthly_limit:
        raise InvalidRequestException({"detail": "Monthly transfer limit exceeded"})

    return True


def create_in_app_notification(company, title, message, notification_type='general', related_object_id=None, related_object_type=None):
    from account.models import InAppNotification

    """
    Helper function to create in-app notifications.

    Args:
        company: Company instance
        title: Notification title
        message: Notification message
        notification_type: Type of notification
        related_object_id: Optional ID of related object
        related_object_type: Optional type of related object

    Returns:
        InAppNotification instance
    """

    logger = logging.getLogger(__name__)

    try:
        notification = InAppNotification.objects.create(
            company=company,
            title=title,
            message=message,
            notification_type=notification_type,
            related_object_id=related_object_id,
            related_object_type=related_object_type
        )
        log_request(f"Created in-app notification for {company.name}: {title}")
        return notification
    except Exception as e:
        logger.error(f"Error creating in-app notification: {str(e)}")
        log_request(f"Error creating in-app notification: {str(e)}")
        return None


def get_superadmin_dashboard_data():
    """
    Get dashboard data for superadmin including:
    - Total transactions (SingleTransfer + BulkTransfer)
    - Total corporate users (active companies)
    - Total airtime (from bill payments)
    - Total bill payments
    - Transaction overview (monthly data)
    - Services breakdown (by bill payment categories)
    - Recent customers (last 10)

    Returns:
        dict: Dashboard data matching the UI requirements
    """
    try:
        from django.db.models import Sum, Q
        from django.utils import timezone
        from calendar import month_name

        from account.models import Company, Customer
        from transfer.models import SingleTransfer, BulkTransfer
        from billpayment.models import BillPayment

        # period = request.GET.get("period", "today")

        current_date = timezone.now()

        # 1. Total Transactions (SingleTransfer + BulkTransfer with success status)
        single_transfers = SingleTransfer.objects.filter(status='success')
        bulk_transfers = BulkTransfer.objects.filter(status='success')

        total_transaction_amount = (
            (single_transfers.aggregate(Sum('amount'))['amount__sum'] or 0) +
            (bulk_transfers.aggregate(Sum('amount'))['amount__sum'] or 0)
        )
        total_transaction_count = single_transfers.count() + bulk_transfers.count()

        # 2. Total Corporate Users (active companies)
        customers = Customer.objects.all().order_by("-user__date_joined")
        total_corporate_users = customers.count()
        active_corporate_users = customers.filter(active=True).count()
        inactive_corporate_users = customers.filter(active=False).count()

        # 3. Total Airtime (from bill payments with airtime category)
        bill_payments = BillPayment.objects.filter(status='success')
        airtime_payments = bill_payments.filter(biller_category_name__icontains='airtime')
        total_airtime_amount = airtime_payments.aggregate(Sum('amount'))['amount__sum'] or 0
        total_airtime_count = airtime_payments.count()

        # 4. Total Bill Payments
        total_bill_payment_amount = bill_payments.aggregate(Sum('amount'))['amount__sum'] or 0
        total_bill_payment_count = bill_payments.count()

        # 5. Transaction Overview
        transfer_graph = dict()
        weekly = []
        monthly = []
        yearly = []

        for delta in range(6, -1, -1):
            week_total = 0
            month_total = 0
            year_total= 0
            week_date = current_date - relativedelta(weeks=delta)
            month_date = current_date - relativedelta(months=delta)
            year_date = current_date - relativedelta(years=delta)
            week_start, week_end = get_week_start_and_end_datetime(week_date)
            month_start, month_end = get_month_start_and_end_datetime(month_date)
            year_start, year_end = get_year_start_and_end_datetime(year_date)

            weekly_single_transaction = single_transfers.filter(created_at__gte=week_start, created_at__lte=week_end).aggregate(Sum('amount'))['amount__sum'] or 0
            weekly_bulk_transaction = bulk_transfers.filter(created_at__gte=week_start, created_at__lte=week_end).aggregate(Sum('amount'))['amount__sum'] or 0
            total = weekly_single_transaction + weekly_bulk_transaction
            if total:
                week_total = total
            weekly.append({'week': week_start.strftime('%d %b'), 'amount': week_total})

            monthly_single_transaction = single_transfers.filter(created_at__gte=month_start, created_at__lte=month_end).aggregate(Sum('amount'))['amount__sum'] or 0
            monthly_bulk_transaction = bulk_transfers.filter(created_at__gte=month_start, created_at__lte=month_end).aggregate(Sum('amount'))['amount__sum'] or 0
            total = monthly_single_transaction + monthly_bulk_transaction
            if total:
                month_total = total
            monthly.append({'month': month_start.strftime('%b'), 'amount': month_total})

            yearly_single_transaction = single_transfers.filter(created_at__gte=year_start, created_at__lte=year_end).aggregate(Sum('amount'))['amount__sum'] or 0
            yearly_bulk_transaction = bulk_transfers.filter(created_at__gte=year_start, created_at__lte=year_end).aggregate(Sum('amount'))['amount__sum'] or 0
            total = yearly_single_transaction + yearly_bulk_transaction
            if total:
                year_total = total
            yearly.append({'year': year_start.strftime('%Y'), 'amount': year_total})

        transfer_graph['weekly'] = weekly
        transfer_graph['monthly'] = monthly
        transfer_graph['yearly'] = yearly

        # 6. Services Breakdown - Based on bill payment categories

        # Get airtime data
        airtime_amount = bill_payments.filter(biller_category_name__icontains='airtime').aggregate(Sum('amount'))['amount__sum'] or 0

        # Get data services
        data_amount = bill_payments.filter(biller_category_name__icontains='data').aggregate(Sum('amount'))['amount__sum'] or 0

        # Get electricity
        electricity_amount = bill_payments.filter(
            Q(biller_category_name__icontains='electricity') |
            Q(biller_category_name__icontains='power')).aggregate(Sum('amount'))['amount__sum'] or 0

        # Get cable/TV
        cable_amount = bill_payments.filter(
            Q(biller_category_name__icontains='cable') |
            Q(biller_category_name__icontains='tv')).aggregate(Sum('amount'))['amount__sum'] or 0

        # Get insurance
        insurance_amount = bill_payments.filter(biller_category_name__icontains='insurance').aggregate(Sum('amount'))['amount__sum'] or 0

        # Get betting
        betting_amount = bill_payments.filter(biller_category_name__icontains='betting').aggregate(Sum('amount'))['amount__sum'] or 0

        # Get education
        education_amount = bill_payments.filter(
            Q(biller_category_name__icontains='education') |
            Q(biller_category_name__icontains='school')).aggregate(Sum('amount'))['amount__sum'] or 0

        # Get tolls
        tolls_amount = bill_payments.filter(
            Q(biller_category_name__icontains='toll') |
            Q(biller_category_name__icontains='transport')).aggregate(Sum('amount'))['amount__sum'] or 0

        # Get event tickets
        event_amount = bill_payments.filter(
            Q(biller_category_name__icontains='event') |
            Q(biller_category_name__icontains='ticket')).aggregate(Sum('amount'))['amount__sum'] or 0

        services_breakdown = [
            {'name': 'Airtime', 'amount': airtime_amount},
            {'name': 'Data', 'amount': data_amount},
            {'name': 'Cable Report', 'amount': cable_amount},
            {'name': 'Electricity', 'amount': electricity_amount},
            {'name': 'Insurance', 'amount': insurance_amount},
            {'name': 'Event', 'amount': event_amount},
            {'name': 'Tolls', 'amount': tolls_amount},
            {'name': 'Betting', 'amount': betting_amount},
            {'name': 'Education', 'amount': education_amount},
            {
                'name': 'Others', 'amount': (
                        total_bill_payment_amount - airtime_amount - data_amount - cable_amount - electricity_amount -
                        insurance_amount - event_amount - tolls_amount - betting_amount - education_amount) or 0
            },
        ]

        # 7. Recent Customers - Last 10 customers
        recent_customers = customers[:10]
        recent_customers_data = []

        for idx, customer in enumerate(recent_customers, 1):
            # Get customer's account number (assuming first company account)
            account_number = ""
            if customer.company:
                company_account = customer.company.companyaccount_set.first()
                if company_account:
                    account_number = company_account.account_number

            recent_customers_data.append({
                'sn': idx,
                'full_name': customer.user.get_full_name(),
                'email': customer.user.email,
                'phone_number': customer.phone_number,
                'date_of_birth': customer.dob.strftime('%d/%m/%Y') if customer.dob else '',
                'account_number': account_number,
                'date_joined': customer.created_at.strftime('%d/%m/%Y'),
            })

        return {
            'total_transactions': {
                'amount': total_transaction_amount,
                'count': total_transaction_count
            },
            'total_corporate_users': {
                'total': total_corporate_users,
                'active': active_corporate_users,
                'inactive': inactive_corporate_users
            },
            'total_airtime': {
                'amount': total_airtime_amount,
                'count': total_airtime_count
            },
            'total_bill_payments': {
                'amount': total_bill_payment_amount,
                'count': total_bill_payment_count
            },
            'transaction_overview': transfer_graph,
            'services_breakdown': services_breakdown,
            'recent_customers': recent_customers_data
        }

    except Exception as e:
        log_request(f"Error generating dashboard data: {str(e)}")
        return {
            'error': f'Failed to generate dashboard data: {str(e)}',
            'total_transactions': {'amount': 0, 'count': 0},
            'total_corporate_users': {'total': 0, 'active': 0, 'inactive': 0},
            'total_airtime': {'amount': 0, 'count': 0},
            'total_bill_payments': {'amount': 0, 'count': 0},
            'transaction_overview': [],
            'services_breakdown': [],
            'recent_customers': []
        }


def log_admin_action(admin_user, action, description, target_model=None, target_id=None,
                    request=None, additional_data=None):
    """Utility function to log admin actions"""
    from superadmin.models import AdminAuditTrail

    ip_address = None
    user_agent = None

    if request:
        # Get IP address
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip_address = x_forwarded_for.split(',')[0]
        else:
            ip_address = request.META.get('REMOTE_ADDR')

        # Get user agent
        user_agent = request.META.get('HTTP_USER_AGENT', '')

    AdminAuditTrail.objects.create(
        admin_user=admin_user,
        action=action,
        description=description,
        target_model=target_model,
        target_id=target_id,
        ip_address=ip_address,
        user_agent=user_agent,
        additional_data=additional_data
    )
