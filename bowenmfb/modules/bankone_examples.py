"""
Example usage of the BankOne API client.

This file demonstrates how to use the BankOneClient for various operations.
"""

from .bankone import get_bankone_client, encrypt_auth_token, decrypt_auth_token
from .exceptions import BankOneAPIError, BankOneAuthenticationError


def example_create_customer():
    """Example: Create an individual customer."""
    client = get_bankone_client()

    try:
        response = client.create_customer(
            last_name="<PERSON><PERSON>",
            other_names="<PERSON>",
            city="Lagos",
            address="123 Main Street, Lagos",
            gender=0,  # 0 for Male, 1 for Female
            date_of_birth="1990-01-15",
            phone_no="***********",
            place_of_birth="Lagos",
            national_identity_no="***********",
            next_of_kin_name="<PERSON>",
            next_of_kin_phone_number="***********",
            referral_name="<PERSON>",
            referral_phone_no="***********",
            bvn="***********",
            email="<EMAIL>"
        )

        print(f"Customer created successfully: {response}")
        return response

    except BankOneAPIError as e:
        print(f"Failed to create customer: {e}")
        return None


def example_create_account():
    """Example: Create an account quickly."""
    client = get_bankone_client()

    try:
        response = client.create_account_quick(
            transaction_tracking_ref="TXN123456",
            account_opening_tracking_ref="ACC123456",
            product_code="101",
            customer_id="004529",  # From customer creation response
            last_name="Doe",
            other_names="John",
            bvn="***********",
            phone_no="***********",
            gender="0",
            place_of_birth="Lagos",
            date_of_birth="1990-01-15",
            address="123 Main Street, Lagos",
            account_officer_code="001",
            email="<EMAIL>"
        )

        print(f"Account created successfully: {response}")
        return response

    except BankOneAPIError as e:
        print(f"Failed to create account: {e}")
        return None


def example_get_account_balance():
    """Example: Get account balance."""
    client = get_bankone_client()

    try:
        response = client.get_account_by_number("**********")

        print(f"Account details: {response}")
        return response

    except BankOneAPIError as e:
        print(f"Failed to get account details: {e}")
        return None


def example_inter_bank_transfer():
    """Example: Perform inter-bank transfer."""
    client = get_bankone_client()

    try:
        response = client.inter_bank_transfer(
            amount=5000.0,  # Will be multiplied by 100 automatically
            payer="John Doe",
            payer_account_number="**********",
            receiver_account_number="**********",
            receiver_bank_code="076",
            narration="Transfer to friend",
            transaction_reference="TF24107924",
            token="your_api_token_here"
        )

        print(f"Transfer successful: {response}")
        return response

    except BankOneAPIError as e:
        print(f"Transfer failed: {e}")
        return None


def example_local_transfer():
    """Example: Perform local/intra-bank transfer."""
    client = get_bankone_client()

    try:
        response = client.local_funds_transfer(
            from_account_number="**********",
            amount=1000.0,  # Will be multiplied by 100 automatically
            to_account_number="**********",
            retrieval_reference="testintra1",
            narration="Local funds transfer",
            authentication_key="c66ef21b-cf4d-420f-9c29-aee513bd6854"
        )

        print(f"Local transfer successful: {response}")
        return response

    except BankOneAPIError as e:
        print(f"Local transfer failed: {e}")
        return None


def example_name_enquiry():
    """Example: Perform name enquiry."""
    client = get_bankone_client()

    try:
        response = client.name_enquiry(
            account_number="********",
            bank_code="678",
            token="your_api_token_here"
        )

        print(f"Name enquiry result: {response}")
        return response

    except BankOneAPIError as e:
        print(f"Name enquiry failed: {e}")
        return None


def example_generate_statement():
    """Example: Generate account statement."""
    client = get_bankone_client()

    try:
        response = client.generate_account_statement(
            account_number="**********",
            from_date="2024-01-01",
            to_date="2024-01-31"
        )

        if response.get("IsSuccessful"):
            # Save the PDF statement
            pdf_data = response.get("Message")
            if pdf_data:
                client.save_pdf_statement(pdf_data, "statement.pdf")
                print("Statement saved as statement.pdf")

        return response

    except BankOneAPIError as e:
        print(f"Failed to generate statement: {e}")
        return None


def example_send_bulk_sms():
    """Example: Send bulk SMS messages."""
    client = get_bankone_client()

    try:
        sms_messages = [
            {
                "AccountNumber": "**********",
                "To": "***********",
                "Body": "Your account balance is N50,000. Thank you for banking with us.",
                "ReferenceNo": "SMS001"
            },
            {
                "AccountNumber": "**********",
                "To": "***********",
                "Body": "Your transfer of N10,000 was successful. Ref: TXN123456",
                "ReferenceNo": "SMS002"
            }
        ]

        response = client.send_bulk_sms(sms_messages)
        print(f"SMS sent successfully: {response}")
        return response

    except BankOneAPIError as e:
        print(f"Failed to send SMS: {e}")
        return None


def example_send_email():
    """Example: Send email messages."""
    client = get_bankone_client()

    try:
        email_messages = [
            {
                "InstitutionCode": "223333",
                "MfbCode": "123422",
                "emailFrom": "<EMAIL>",
                "emailTo": "<EMAIL>",
                "Subject": "Account Statement",
                "Message": "Dear Customer, please find your monthly account statement attached. Thank you for banking with us."
            },
            {
                "InstitutionCode": "223333",
                "MfbCode": "123422",
                "emailFrom": "<EMAIL>",
                "emailTo": "<EMAIL>",
                "Subject": "Transfer Notification",
                "Message": "Your transfer request has been processed successfully. Reference: TXN789012"
            }
        ]

        response = client.send_email(email_messages)
        print(f"Email sent successfully: {response}")
        return response

    except BankOneAPIError as e:
        print(f"Failed to send email: {e}")
        return None


def example_check_bvn_exists():
    """Example: Check if BVN already exists in the system."""
    client = get_bankone_client()

    try:
        # Test with a sample BVN
        bvn = "***********"
        response = client.check_bvn_exists(bvn)

        print(f"BVN Check Response: {response}")

        if response.get("IsSuccessful"):
            customer_id = response.get("CustomerIDInString")
            print(f"✅ BVN {bvn} exists for customer: {customer_id}")
        else:
            message = response.get("Message", "BVN not found")
            print(f"❌ BVN {bvn} not found: {message}")

        return response

    except BankOneAPIError as e:
        print(f"Failed to check BVN: {e}")
        return None


def example_check_email_exists():
    """Example: Check if email already exists in the system."""
    client = get_bankone_client()

    try:
        # Test with sample emails
        test_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]

        results = {}

        for email in test_emails:
            exists = client.check_email_exists(email)
            results[email] = exists

            if exists:
                print(f"✅ Email {email} already exists in the system")
            else:
                print(f"❌ Email {email} is available")

        return results

    except BankOneAPIError as e:
        print(f"Failed to check email: {e}")
        return None


def example_check_phone_exists():
    """Example: Check if phone number already exists in the system."""
    client = get_bankone_client()

    try:
        # Test with sample phone numbers
        test_phones = [
            "***********",
            "***********",
            "***********"
        ]

        results = {}

        for phone in test_phones:
            exists = client.check_phone_exists(phone)
            results[phone] = exists

            if exists:
                print(f"✅ Phone {phone} already exists in the system")
            else:
                print(f"❌ Phone {phone} is available")

        return results

    except BankOneAPIError as e:
        print(f"Failed to check phone: {e}")
        return None


def example_customer_validation_workflow():
    """Example: Complete customer validation workflow before registration."""
    client = get_bankone_client()

    try:
        # Sample customer data to validate
        customer_data = {
            "bvn": "***********",
            "email": "<EMAIL>",
            "phone": "***********"
        }

        print("=== Customer Validation Workflow ===")
        print(f"Validating customer data: {customer_data}")

        # Step 1: Check BVN
        print("\n1. Checking BVN...")
        bvn_response = client.check_bvn_exists(customer_data["bvn"])
        bvn_exists = bvn_response.get("IsSuccessful", False)

        if bvn_exists:
            print(f"⚠️  BVN {customer_data['bvn']} already exists")
            existing_customer = bvn_response.get("CustomerIDInString")
            print(f"   Existing customer ID: {existing_customer}")
        else:
            print(f"✅ BVN {customer_data['bvn']} is available")

        # Step 2: Check Email
        print("\n2. Checking Email...")
        email_exists = client.check_email_exists(customer_data["email"])

        if email_exists:
            print(f"⚠️  Email {customer_data['email']} already exists")
        else:
            print(f"✅ Email {customer_data['email']} is available")

        # Step 3: Check Phone
        print("\n3. Checking Phone...")
        phone_exists = client.check_phone_exists(customer_data["phone"])

        if phone_exists:
            print(f"⚠️  Phone {customer_data['phone']} already exists")
        else:
            print(f"✅ Phone {customer_data['phone']} is available")

        # Summary
        print("\n=== Validation Summary ===")
        validation_results = {
            "bvn_available": not bvn_exists,
            "email_available": not email_exists,
            "phone_available": not phone_exists,
            "can_proceed": not bvn_exists and not email_exists and not phone_exists
        }

        if validation_results["can_proceed"]:
            print("✅ All validations passed - Customer can be registered")
        else:
            print("❌ Validation failed - Customer data conflicts detected")
            if bvn_exists:
                print("   - BVN already in use")
            if email_exists:
                print("   - Email already in use")
            if phone_exists:
                print("   - Phone already in use")

        return validation_results

    except BankOneAPIError as e:
        print(f"Failed to validate customer: {e}")
        return None


def example_transaction_status():
    """Example: Check transaction status."""
    client = get_bankone_client()

    try:
        # For inter-bank transaction
        response = client.inter_bank_transaction_status(
            amount=5000.0,
            retrieval_reference="TF24107924",
            transaction_date="2024-01-15",
            token="your_api_token_here"
        )

        print(f"Transaction status: {response}")
        return response

    except BankOneAPIError as e:
        print(f"Failed to check transaction status: {e}")
        return None


def example_freeze_account():
    """Example: Freeze an account."""
    client = get_bankone_client()

    try:
        response = client.freeze_account(
            account_no="**********",
            authentication_code="XXXXXX-XXXX-XXXX-XXXX-XXXXXXXXX",
            reference_id="********",
            reason="suspected fraud"
        )

        print(f"Account freeze result: {response}")
        return response

    except BankOneAPIError as e:
        print(f"Failed to freeze account: {e}")
        return None


def example_environment_configuration():
    """Example: Using different environments and configurations."""

    # Use dev environment explicitly
    dev_client = get_bankone_client(environment='dev')
    print(f"Dev client base URL: {dev_client.base_url}")

    # Use production environment explicitly
    prod_client = get_bankone_client(environment='prod')
    print(f"Production client base URL: {prod_client.base_url}")

    # Auto-detect environment (based on Django DEBUG setting)
    auto_client = get_bankone_client()
    print(f"Auto-detected environment: {auto_client.environment}")
    print(f"Auto-detected base URL: {auto_client.base_url}")


def example_token_encryption():
    """Example: Encrypting and decrypting authentication tokens."""

    # Example token (replace with actual token)
    plain_token = "your_actual_bankone_auth_token_here"

    try:
        # Encrypt token for storage
        encrypted_token = encrypt_auth_token(plain_token)
        print(f"Encrypted token: {encrypted_token[:50]}...")  # Show first 50 chars

        # Decrypt token for use
        decrypted_token = decrypt_auth_token(encrypted_token)
        print(f"Decrypted token matches: {decrypted_token == plain_token}")

        # Store encrypted token in database
        from account.models import BankConstantTable

        # Get or create bank config
        bank_config, created = BankConstantTable.objects.get_or_create(
            name="BowenMFB",
            defaults={
                'auth_token': encrypted_token,
                'institution_code': 'your_institution_code'
            }
        )

        if not created:
            # Update existing config with encrypted token
            bank_config.auth_token = encrypted_token
            bank_config.save()

        print("✓ Encrypted token stored in database")

        # Test that client can decrypt and use the token
        client = get_bankone_client()
        decrypted_from_db = client.auth_token  # This will decrypt automatically
        print(f"✓ Client can decrypt token from database: {len(decrypted_from_db)} chars")

    except Exception as e:
        print(f"Token encryption example failed: {e}")


def example_database_configuration():
    """Example: Configure base URLs in database."""

    try:
        from account.models import BankConstantTable

        # Get or create bank configuration
        bank_config, created = BankConstantTable.objects.get_or_create(
            name="BowenMFB",
            defaults={
                'dev_base_url': 'https://staging.mybankone.com',
                'prod_base_url': 'https://mybankone.com',
                'institution_code': 'your_institution_code'
            }
        )

        if not created:
            # Update existing configuration
            bank_config.dev_base_url = 'https://staging.mybankone.com'
            bank_config.prod_base_url = 'https://mybankone.com'
            bank_config.save()

        print("✓ Base URLs configured in database:")
        print(f"  Dev URL: {bank_config.dev_base_url}")
        print(f"  Prod URL: {bank_config.prod_base_url}")

        # Test that client uses database URLs
        dev_client = get_bankone_client(environment='dev')
        prod_client = get_bankone_client(environment='prod')

        print(f"\n✓ Dev client uses: {dev_client.base_url}")
        print(f"✓ Prod client uses: {prod_client.base_url}")

    except Exception as e:
        print(f"Database configuration example failed: {e}")


def example_configuration_setup():
    """Example: Complete configuration setup for deployment."""

    print("BankOne Configuration Setup Guide:")
    print("=" * 50)

    print("\n1. Configure Base URLs in Database (Recommended):")
    print("   from account.models import BankConstantTable")
    print("   config = BankConstantTable.objects.create(")
    print("       name='BowenMFB',")
    print("       dev_base_url='https://staging.mybankone.com',")
    print("       prod_base_url='https://mybankone.com',")
    print("       institution_code='your_institution_code'")
    print("   )")

    print("\n2. Alternative: Environment Variables (.env file):")
    print("   BANKONE_BASE_URL=https://staging.mybankone.com  # Fallback URL")

    print("\n3. Encrypt your authentication token:")
    print("   from bowenmfb.modules.bankone import encrypt_auth_token")
    print("   encrypted_token = encrypt_auth_token('your_actual_token')")
    print("   config.auth_token = encrypted_token")
    print("   config.save()")

    print("\n4. Use the client:")
    print("   from bowenmfb.modules.bankone import get_bankone_client")
    print("   client = get_bankone_client()  # Auto-detects environment")
    print("   # Uses database URLs, token is automatically decrypted")

    print("\n5. Environment Detection:")
    print("   - 'dev' environment when DEBUG=True")
    print("   - 'prod' environment when DEBUG=False")
    print("   - Or specify explicitly: get_bankone_client(environment='dev')")


if __name__ == "__main__":
    # Run examples (uncomment as needed)
    # example_create_customer()
    # example_create_account()
    # example_get_account_balance()
    # example_inter_bank_transfer()
    # example_local_transfer()
    # example_name_enquiry()
    # example_generate_statement()
    # example_transaction_status()
    # example_freeze_account()

    # Messaging examples
    # example_send_bulk_sms()
    # example_send_email()

    # Customer validation examples
    # example_check_bvn_exists()
    # example_check_email_exists()
    # example_check_phone_exists()
    # example_customer_validation_workflow()

    # New examples for encryption and environment configuration
    # example_environment_configuration()
    # example_token_encryption()
    # example_database_configuration()
    # example_configuration_setup()
    pass
