APPROVAL_STATUS_CHOICES = (
    ('approved', 'Approved'), ('declined', 'Declined'), ('pending', 'Pending')
)

GENDER_TYPE_CHOICES = (
    ('male', 'Male'), ('female', 'Female')
)

STATUS_CHOICES = (
    ('pending', 'Pending'), ('failed', 'Failed'), ('success', 'Success')
)

TRANSFER_BENEFICIARY_TYPE_CHOICES = (
    ('intra', 'Local Transfer'), ('inter', 'Inter Bank Transfer')
)

BILL_PAYMENT_BENEFICIARY_TYPE_CHOICES = (
    ('airtime', 'Airtime'), ('data', 'Data'), ('electricity', 'Electricity'), ('cable', 'Cable'), ('betting', 'Betting'), ('others', 'Others')
)

USER_TYPE_CHOICES = (
    ("uploader", "Uploader"), ("verifier", "Verifier"), ("authorizer", "Authorizer")
)

TRANSFER_REQUEST_STATUS = (
    ("pending", "Pending"), ("approved", "Approved"), ("declined", "Declined")
)

SCHEDULE_TYPE = (
    ("daily", "Daily"), ("weekly", "Weekly"), ("monthly", "Monthly"), ("quarterly", "Quarterly"),
    ("bi-annually", "Bi-annually"), ("yearly", "Yearly"), ("once", "One Time")
)

DAYS_OF_THE_MONTH_CHOICES = (
    ('1', '1'), ('2', '2'), ('3', '3'), ('4', '4'), ('5', '5'), ('6', '6'), ('7', '7'), ('8', '8'),
    ('9', '9'), ('10', '10'), ('11', '11'), ('12', '12'), ('13', '13'), ('14', '14'), ('15', '15'), ('16', '16'),
    ('17', '17'), ('18', '18'), ('19', '19'), ('20', '20'), ('21', '21'), ('22', '22'), ('23', '23'), ('24', '24'),
    ('25', '25'), ('26', '26'), ('27', '27'), ('28', '28')
)

DAY_OF_THE_WEEK_CHOICES = (
    ('1', 'Monday'), ('2', 'Tuesday'), ('3', 'Wednesday'), ('4', 'Thursday'), ('5', 'Friday'), ('6', 'Saturday'),
    ('7', 'Sunday')
)

TRANSFER_SCHEDULE_STATUS = (
    ("active", "Active"), ("inactive", "Inactive")
)

TRANSFER_SCHEDULE_CATEGORY_CHOICES = (
    ("beneficiary_payment", "Beneficiary Payment"), ("employee_payment", "Employee Payment"), ("vendor_payment", "Vendor Payment")
)

ADMIN_ROLE_CHOICES = (
    ("checker", "Checker"), ("verifier", "Verifier"), ("approver", "Approver")
)

ADMIN_PERMISSION_CHOICES = (
    ("view_companies", "View Companies"),
    ("manage_company_users", "Set Company Users as Inactive/Active"),
    ("check_account_requests", "Check Account Creation Requests"),
    ("verify_account_requests", "Verify Account Creation Requests"),
    ("approve_account_requests", "Approve Account Creation Requests"),
    ("view_transfers", "List Company Transfers"),
    ("view_bill_payments", "List Company Bill Payments"),
    ("manage_admin_permissions", "Change Admin Permissions"),
    ("create_admin_users", "Create New Admin Users"),
    ("view_audit_trail", "View Admin Audit Trail"),
    ("update_transfer_limit", "Update Transfer Limit"),
    ("update_signatories", "Manage Account Signatories"),
)

SIGNATORY_ROLE_CHOICES = (
    ("uploader", "Uploader"),
    ("checker", "Checker"),
    ("verifier", "Verifier"),
    ("approver", "Approver"),
)

HIERARCHY_LEVEL_CHOICES = (
    (1, "Level 1 - Uploader"),
    (2, "Level 2 - Checker"),
    (3, "Level 3 - Verifier"),
    (4, "Level 4 - Approver"),
    (5, "Level 5 - Final Approver"),
)

WORKFLOW_STATUS_CHOICES = (
    ("pending", "Pending"),
    ("in_progress", "In Progress"),
    ("approved", "Approved"),
    ("declined", "Declined"),
    ("cancelled", "Cancelled"),
)

AUDIT_ACTION_CHOICES = (
    ('login', 'Admin Login'),
    ('logout', 'Admin Logout'),
    ('create_admin', 'Create Admin User'),
    ('update_admin', 'Update Admin User'),
    ('change_permissions', 'Change Admin Permissions'),
    ('set_customer_active', 'Set Customer Active'),
    ('set_customer_inactive', 'Set Customer Inactive'),
    ('check_account_request', 'Check Account Request'),
    ('verify_account_request', 'Verify Account Request'),
    ('approve_account_request', 'Approve Account Request'),
    ('decline_account_request', 'Decline Account Request'),
    ('view_transfers', 'View Transfers'),
    ('view_bill_payments', 'View Bill Payments'),
    ('change_password', 'Change Password'),
    ('view_customers', 'View Customers'),
    ('view_companies', 'View Companies'),
    ('view_audit_trail', 'View Audit Trail'),
)

IN_APP_NOTIFICATION_CHOICES = (
        ('transfer_created', 'Transfer Created'),
        ('transfer_checked', 'Transfer Checked'),
        ('transfer_verified', 'Transfer Verified'),
        ('transfer_approved', 'Transfer Approved'),
        ('bill_payment_created', 'Bill Payment Created'),
        ('bill_payment_checked', 'Bill Payment Checked'),
        ('bill_payment_verified', 'Bill Payment Verified'),
        ('bill_payment_approved', 'Bill Payment Approved'),
        ('user_signup', 'User Signup'),
        ('transfer_limit_updated', 'Transfer Limit Updated'),
        ('general', 'General'),
    )

