from django.db.models import Q
from django.utils import timezone
from drf_spectacular.utils import extend_schema, OpenApiParameter
from rest_framework import status, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.generics import ListAPIView, CreateAPIView, RetrieveAPIView, DestroyAPIView
from django.db import transaction

from bowenmfb.modules.paginations import CustomPagination
from bowenmfb.modules.permissions import HasCompanyAccount
from bowenmfb.modules.utils import get_account_balance, check_transaction_limit
from .models import (
    BillerCategory, <PERSON>er, PaymentItem, BillPaymentRequest,
    BillPayment, BillPaymentApprovalWorkflow, BillPaymentBeneficiary
)
from .serializers import (
    BillerCategorySerializer, BillerSerializer, PaymentItemSerializer,
    BillPaymentRequestSerializerIn, BillPaymentRequestSerializerOut,
    BillPaymentSerializer, BillPaymentApprovalWorkflowSerializerOut,
    ProcessBillPaymentApprovalSerializerIn, BankOneBillerCategorySerializer,
    BankOneBillerSerializer, BankOnePaymentItemSerializer, BillPaymentBeneficiarySerializerIn, BillPaymentBeneficiarySerializerOut
)
from transfer.models import SignatoryHierarchy, AccountSignatory
from bowenmfb.modules.bankone import get_bankone_client
from bowenmfb.modules.exceptions import BankOneAPIError, raise_serializer_error_msg, InvalidRequestException
from .tasks import process_bill_payment_task


class BillerCategoryListAPIView(ListAPIView):
    """
    API view to list all biller categories.
    """
    queryset = BillerCategory.objects.all()
    serializer_class = BillerCategorySerializer
    permission_classes = [IsAuthenticated]


@extend_schema(
    parameters=[
        OpenApiParameter(name="category_id", type=str, required=True, description="Biller CategoryID"),
    ],
    responses={status.HTTP_200_OK: BillerSerializer}
)
class BillerListAPIView(ListAPIView):
    """
    API view to list all billers, optionally filtered by category.
    """
    serializer_class = BillerSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        category_id = self.request.query_params.get('category_id')
        if category_id:
            return Biller.objects.filter(category_id=category_id)
        return Biller.objects.none()



@extend_schema(
    parameters=[
        OpenApiParameter(name="biller_id", type=str, required=True, description="BillerID"),
    ],
    responses={status.HTTP_200_OK: BillerSerializer}
)
class PaymentItemListAPIView(ListAPIView):
    """
    API view to list payment items for a specific biller.
    """
    serializer_class = PaymentItemSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        biller_id = self.request.query_params.get('biller_id')
        if biller_id:
            return PaymentItem.objects.filter(biller_id=biller_id)
        return PaymentItem.objects.none()


class BillPaymentRequestCreateAPIView(CreateAPIView):
    """
    API view to create a new bill payment request.
    """
    queryset = BillPaymentRequest.objects.all()
    serializer_class = BillPaymentRequestSerializerIn
    permission_classes = [HasCompanyAccount]

    def perform_create(self, serializer):
        """Create bill payment request and associated workflow."""
        # Check transaction limit
        amount = serializer.validated_data['amount']
        account = serializer.validated_data['from_account']

        # Check if logged in user is a signatory of the from_account and can upload transfer requests
        try:
            signatory = AccountSignatory.objects.get(company_account=account, customer=self.request.user.customer, is_active=True)
            if not signatory.can_upload:
                raise InvalidRequestException({"detail": "You are not authorized to perform this action"})
        except AccountSignatory.DoesNotExist:
            raise InvalidRequestException({"detail": "You are not authorized to perform this action"})

        check_transaction_limit(account.id, amount)

        with transaction.atomic():
            # Create the bill payment request
            bill_payment_request = serializer.save()

            # Create the approval workflow
            workflow = BillPaymentApprovalWorkflow.objects.create(
                bill_payment_request=bill_payment_request,
                uploaded_by=self.request.user.customer,
                current_level=1,
                status='pending'
            )

            # Check if single signatory account - auto-approve if so
            try:
                hierarchy = SignatoryHierarchy.objects.get(
                    company_account=bill_payment_request.from_account
                )
                if hierarchy.is_single_signatory:
                    workflow.status = 'approved'
                    workflow.approved_by = self.request.user.customer
                    workflow.approved_at = timezone.now()
                    workflow.save()

                    # Mark the request as approved
                    bill_payment_request.is_approved = True
                    bill_payment_request.approved_by = self.request.user.customer
                    bill_payment_request.approved_at = timezone.now()
                    bill_payment_request.save()

                    # Trigger bill payment processing
                    process_bill_payment_task.delay(str(bill_payment_request.id))
                else:
                    # Multi-signatory account - set to level 2 for checking
                    workflow.current_level = 2
                    workflow.status = 'in_progress'
                    workflow.save()
            except SignatoryHierarchy.DoesNotExist:
                # Default to single signatory if no hierarchy defined
                workflow.status = 'approved'
                workflow.approved_by = self.request.user.customer
                workflow.approved_at = timezone.now()
                workflow.save()

                bill_payment_request.is_approved = True
                bill_payment_request.approved_by = self.request.user.customer
                bill_payment_request.approved_at = timezone.now()
                bill_payment_request.save()

                # Trigger bill payment processing
                process_bill_payment_task.delay(str(bill_payment_request.id))


@extend_schema(
    parameters=[
        OpenApiParameter(name="search", type=str, description="Search by customer name and customerID"),
        OpenApiParameter(name="date_from", type=str, description="filter date created: YYYY-MM-DD"),
        OpenApiParameter(name="date_to", type=str, description="filter date created: YYYY-MM-DD"),
        OpenApiParameter(name="amount_from", type=str, description="filter by amount from"),
        OpenApiParameter(name="amount_to", type=str, description="filter by amount to"),
    ],
    responses={status.HTTP_200_OK: BillPaymentRequestSerializerOut}
)
class BillPaymentRequestListAPIView(ListAPIView):
    """
    API view to list bill payment requests for the user's company.
    """
    serializer_class = BillPaymentRequestSerializerOut
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        query = Q(company_id=self.request.user.customer.company_id)
        search = self.request.GET.get("search", None)
        date_from = self.request.GET.get("date_from", None)
        date_to = self.request.GET.get("date_to", None)
        amount_from = self.request.GET.get("amount_from", None)
        amount_to = self.request.GET.get("amount_to", None)

        try:
            if search:
                query &= Q(customer_name__icontains=search) | Q(customer_id__icontains=search)
            if date_from and date_to:
                query &= Q(created_at__gte=date_from, created_at__lte=date_to)
            if amount_from and amount_to:
                query &= Q(amount__gte=amount_from, amount__lte=amount_to)

            return BillPaymentRequest.objects.filter(query).order_by("-created_at")
        except:
            return BillPaymentRequest.objects.none()


class BillPaymentRequestDetailAPIView(RetrieveAPIView):
    """
    API view to get details of a specific bill payment request.
    """
    serializer_class = BillPaymentRequestSerializerOut
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        try:
            customer = self.request.user.customer
            return BillPaymentRequest.objects.filter(company=customer.company)
        except:
            return BillPaymentRequest.objects.none()


class BillPaymentApprovalWorkflowAPIView(RetrieveAPIView):
    """
    API view to get approval workflow details for a bill payment request.
    """
    serializer_class = BillPaymentApprovalWorkflowSerializerOut
    permission_classes = [IsAuthenticated]
    lookup_field = 'bill_payment_request_id'

    def get_queryset(self):
        try:
            customer = self.request.user.customer
            return BillPaymentApprovalWorkflow.objects.filter(
                bill_payment_request__company=customer.company
            )
        except:
            return BillPaymentApprovalWorkflow.objects.none()


class ProcessBillPaymentApprovalAPIView(APIView):
    """
    API view to process bill payment approval actions (check, verify, approve, decline).
    """
    permission_classes = [IsAuthenticated]

    @extend_schema(request=ProcessBillPaymentApprovalSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request, workflow_id):
        try:
            # Get the workflow
            customer = request.user.customer
            workflow = BillPaymentApprovalWorkflow.objects.get(
                id=workflow_id, bill_payment_request__company=customer.company, status__in=["pending", "in_progress"]
            )

            # Validate input
            serializer = ProcessBillPaymentApprovalSerializerIn(data=request.data, context={"request": request})
            serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
            action = serializer.validated_data['action']
            decline_reason = serializer.validated_data.get('decline_reason', '')

            # Check if user can perform this action
            if not workflow.can_user_perform_action(request.user, action):
                return Response({'detail': f'You do not have permission to {action} this bill payment.'}, status=status.HTTP_403_FORBIDDEN)

            # Check account balance before approval
            if action == 'approve':
                account_balance_check = get_account_balance(workflow.bill_payment_request.from_account.account_number)
                balance = account_balance_check.get("balances").get("withdrawable_balance")
                if balance < workflow.bill_payment_request.amount:
                    return Response(
                        {'detail': f'Insufficient balance. Available: ₦{balance:,.2f}, Required: ₦{workflow.bill_payment_request.amount:,.2f}'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # Process the action
            with transaction.atomic():
                bill_request = workflow.bill_payment_request
                current_time = timezone.now()

                if action == 'decline':
                    # Decline the bill payment
                    if workflow.approved_by:
                        return Response({'detail': 'Cannot decline approved bill payment.'}, status=status.HTTP_400_BAD_REQUEST)

                    workflow.status = 'declined'
                    workflow.declined_by = customer
                    workflow.declined_at = current_time
                    workflow.decline_reason = decline_reason
                    workflow.save()

                    bill_request.is_declined = True
                    bill_request.declined_by = customer
                    bill_request.declined_at = current_time
                    bill_request.decline_reason = decline_reason
                    bill_request.save()

                    return Response({'detail': 'Bill payment declined successfully.'})

                elif action == 'check':
                    # Check the bill payment
                    if workflow.approved_by:
                        return Response({'detail': 'Cannot check approved bill payment.'}, status=status.HTTP_400_BAD_REQUEST)
                    if workflow.declined_by:
                        return Response({'detail': 'Cannot check declined bill payment.'}, status=status.HTTP_400_BAD_REQUEST)

                    workflow.checked_by = customer
                    workflow.checked_at = current_time
                    workflow.current_level = 3
                    workflow.save()

                    bill_request.is_checked = True
                    bill_request.checked_by = customer
                    bill_request.checked_at = current_time
                    bill_request.save()

                    return Response({'detail': 'Bill payment checked successfully.'})

                elif action == 'verify':
                    # Verify the bill payment
                    if workflow.approved_by:
                        return Response({'detail': 'Cannot verify approved bill payment.'}, status=status.HTTP_400_BAD_REQUEST)
                    if workflow.declined_by:
                        return Response({'detail': 'Cannot verify declined bill payment.'}, status=status.HTTP_400_BAD_REQUEST)

                    workflow.verified_by = customer
                    workflow.verified_at = current_time
                    workflow.current_level = 4
                    workflow.save()

                    bill_request.is_verified = True
                    bill_request.verified_by = customer
                    bill_request.verified_at = current_time
                    bill_request.save()

                    return Response({'detail': 'Bill payment verified successfully.'})

                elif action == 'approve':
                    # Approve the bill payment
                    if workflow.declined_by:
                        return Response({'detail': 'Cannot approve declined bill payment.'}, status=status.HTTP_400_BAD_REQUEST)

                    workflow.status = 'approved'
                    workflow.approved_by = customer
                    workflow.approved_at = current_time
                    workflow.save()

                    bill_request.is_approved = True
                    bill_request.approved_by = customer
                    bill_request.approved_at = current_time
                    bill_request.save()

                    # Trigger bill payment processing
                    process_bill_payment_task.delay(str(bill_request.id))

                    return Response({'detail': 'Bill payment approved successfully. Processing payment...'})


        except BillPaymentApprovalWorkflow.DoesNotExist:
            return Response({'error': 'Bill payment workflow not found.'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': f'An error occurred: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    parameters=[
        OpenApiParameter(name="search", type=str, description="Search by customer name, biller name and customerID"),
        OpenApiParameter(name="date_from", type=str, description="filter date created: YYYY-MM-DD"),
        OpenApiParameter(name="date_to", type=str, description="filter date created: YYYY-MM-DD"),
        OpenApiParameter(name="amount_from", type=str, description="filter by amount from"),
        OpenApiParameter(name="amount_to", type=str, description="filter by amount to"),
        OpenApiParameter(name="status", type=str, description="filter by status"),
    ],
    responses={status.HTTP_200_OK: BillPaymentSerializer}
)
class BillPaymentListAPIView(ListAPIView):
    """
    API view to list executed bill payments for the user's company.
    """
    serializer_class = BillPaymentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        query = Q(company_id=self.request.user.customer.company_id)
        search = self.request.GET.get("search", None)
        date_from = self.request.GET.get("date_from", None)
        date_to = self.request.GET.get("date_to", None)
        amount_from = self.request.GET.get("amount_from", None)
        amount_to = self.request.GET.get("amount_to", None)
        query_status = self.request.GET.get("status", None)

        try:
            if search:
                query &= Q(customer_name__icontains=search) | Q(customer_id__icontains=search) | Q(biller_name__icontains=search)
            if date_from and date_to:
                query &= Q(created_at__gte=date_from, created_at__lte=date_to)
            if amount_from and amount_to:
                query &= Q(amount__gte=amount_from, amount__lte=amount_to)
            if query_status:
                query &= Q(status=query_status)

            return BillPayment.objects.filter(query).order_by("-created_at")
        except:
            return BillPayment.objects.none()


# BankOne API Integration Views

@api_view(['GET'])
@permission_classes([])
def sync_payment_items(request):
    """
    Sync payment items from BankOne API.
    """
    try:
        client = get_bankone_client()
        response = client.get_payment_items()

        if not isinstance(response, list):
            return Response(
                {'error': 'Invalid response format from BankOne API'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        created_count = 0
        updated_count = 0

        for item_data in response:
            serializer = BankOnePaymentItemSerializer(data=item_data)
            if serializer.is_valid():
                data = serializer.validated_data

                # Get biller
                try:
                    biller = Biller.objects.get(biller_id=data['BillerId'])
                except Biller.DoesNotExist:
                    # Skip if biller doesn't exist
                    continue

                if biller:
                    payment_item, created = PaymentItem.objects.update_or_create(
                        item_id=str(data['ID'])[:50],
                        biller=biller,
                        defaults={
                            'code': str(data['Code'])[:50],
                            'name': str(data['Name'])[:200],
                            'amount': data.get('Amount', 0.0)
                        }
                    )
                    if created:
                        created_count += 1
                    else:
                        updated_count += 1

        return Response({
            'message': f'Sync completed. Created: {created_count}, Updated: {updated_count}',
            'created': created_count,
            'updated': updated_count
        })

    except BankOneAPIError as e:
        return Response(
            {'error': f'BankOne API error: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    except Exception as e:
        return Response(
            {'error': f'An error occurred: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([])
def sync_biller_categories(request):
    """
    Sync biller categories from BankOne API.
    """
    try:
        client = get_bankone_client()
        response = client.get_biller_categories()

        if not isinstance(response, list):
            return Response(
                {'error': 'Invalid response format from BankOne API'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        created_count = 0
        updated_count = 0

        for category_data in response:
            serializer = BankOneBillerCategorySerializer(data=category_data)
            if serializer.is_valid():
                data = serializer.validated_data
                category, created = BillerCategory.objects.update_or_create(
                    category_id=data['ID'],
                    defaults={
                        'name': data['Name'],
                        'description': data.get('Description', ''),
                        'is_airtime': data.get('IsAirtime', False)
                    }
                )
                if created:
                    created_count += 1
                else:
                    updated_count += 1

        return Response({
            'message': f'Sync completed. Created: {created_count}, Updated: {updated_count}',
            'created': created_count,
            'updated': updated_count
        })

    except BankOneAPIError as e:
        return Response(
            {'error': f'BankOne API error: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    except Exception as e:
        return Response(
            {'error': f'An error occurred: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([])
def sync_billers(request):
    """
    Sync billers from BankOne API.
    """
    try:
        client = get_bankone_client()
        response = client.get_billers()

        if not isinstance(response, list):
            return Response(
                {'error': 'Invalid response format from BankOne API'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        created_count = 0
        updated_count = 0

        for biller_data in response:
            serializer = BankOneBillerSerializer(data=biller_data)
            if serializer.is_valid():
                data = serializer.validated_data
                category = None

                # Get or create category
                try:
                    category = BillerCategory.objects.get(category_id=data['CategoryId'])
                except BillerCategory.DoesNotExist:
                    pass

                if category:
                    biller, created = Biller.objects.update_or_create(
                        biller_id=str(data['ID'])[:50],
                        defaults={
                            'category': category,
                            'name': str(data['Name'])[:200],
                            'short_name': str(data.get('ShortName', ''))[:50],
                            'narration': str(data.get('Narration', ''))[:200],
                            'currency_code': str(data.get('CurrencyCode', '566'))[:10],
                            'currency_symbol': str(data.get('CurrencySymbol', 'NGN'))[:10],
                            'customer_field1': str(data.get('CustomerField1', ''))[:200],
                            'customer_field2': str(data.get('CustomerField2', ''))[:200],
                            'support_email': str(data.get('SupportEmail', ''))[:100],
                            'surcharge': data.get('Surcharge', 0.0),
                            'logo_url': str(data.get('LogoUrl', ''))[:100],
                            'is_active': data.get('IsActive', True)
                        }
                    )
                    if created:
                        created_count += 1
                    else:
                        updated_count += 1

        return Response({
            'message': f'Sync completed. Created: {created_count}, Updated: {updated_count}',
            'created': created_count,
            'updated': updated_count
        })

    except BankOneAPIError as e:
        return Response(
            {'error': f'BankOne API error: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    except Exception as e:
        return Response(
            {'error': f'An error occurred: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


class AddBillPaymentBeneficiaryAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=BillPaymentBeneficiarySerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = BillPaymentBeneficiarySerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


@extend_schema(
    parameters=[
        OpenApiParameter(name="beneficiary_type", type=str, description="airtime, data, electricity, cable, betting, others. Default is 'airtime'"),
        OpenApiParameter(name="search", type=str, description="Search by customer name and customerID")

    ],
    responses={status.HTTP_200_OK: BillPaymentBeneficiarySerializerOut}
)
class BillPaymentBeneficiaryListAPIView(ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = BillPaymentBeneficiarySerializerOut
    pagination_class = CustomPagination
    lookup_field = "id"
    filter_backends = [filters.SearchFilter]
    search_fields = ["customer_name", "customer_id"]

    def get_queryset(self):
        benefactor_type = self.request.GET.get("beneficiary_type", "airtime")
        return BillPaymentBeneficiary.objects.filter(company_id=self.request.user.customer.company_id, beneficiary_type=benefactor_type).order_by("customer_name")


class BillPaymentBeneficiaryDestroyAPIView(DestroyAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = BillPaymentBeneficiarySerializerOut
    lookup_field = "id"

    def get_queryset(self):
        return BillPaymentBeneficiary.objects.get(company_id=self.request.user.customer.company_id, id=self.kwargs["id"])

    def delete(self, request, *args, **kwargs):
        instance = self.get_queryset()
        instance.delete()
        return Response({"detail": "Beneficiary deleted successfully"})




