from django.urls import path
from . import views


app_name = "billpayment"

urlpatterns = [
    # Biller data endpoints
    path('categories/', views.BillerCategoryListAPIView.as_view(), name='biller-categories'),
    path('billers/', views.BillerListAPIView.as_view(), name='billers'),
    path('payment-items/', views.PaymentItemListAPIView.as_view(), name='payment-items'),

    # Bill payment request endpoints
    path('requests/', views.BillPaymentRequestListAPIView.as_view(), name='bill-payment-requests'),
    path('requests/create/', views.BillPaymentRequestCreateAPIView.as_view(), name='create-bill-payment-request'),
    path('requests/<uuid:pk>/', views.BillPaymentRequestDetailAPIView.as_view(), name='bill-payment-request-detail'),

    # Approval workflow endpoints
    path('workflows/<uuid:bill_payment_request_id>/', views.BillPaymentApprovalWorkflowAPIView.as_view(), name='bill-payment-workflow'),
    path('workflows/<uuid:workflow_id>/process/', views.ProcessBillPaymentApprovalAPIView.as_view(), name='process-bill-payment-approval'),

    # Bill payment history
    path('payments/', views.BillPaymentListAPIView.as_view(), name='bill-payments'),

    # BankOne API sync endpoints
    path('sync/categories/', views.sync_biller_categories, name='sync-biller-categories'),
    path('sync/billers/', views.sync_billers, name='sync-billers'),
    path('sync/payment-items/', views.sync_payment_items, name='sync-payment-items'),

    # Bill payment beneficiaries
    path('beneficiaries/add/', views.AddBillPaymentBeneficiaryAPIView.as_view(), name='add-bill-payment-beneficiary'),
    path('beneficiaries/list/', views.BillPaymentBeneficiaryListAPIView.as_view(), name='bill-payment-beneficiaries'),
    path('beneficiaries/list/<uuid:id>/', views.BillPaymentBeneficiaryListAPIView.as_view(), name='bill-payment-beneficiaries-detail'),
    path('beneficiaries/delete/<uuid:id>/', views.BillPaymentBeneficiaryDestroyAPIView.as_view(), name='bill-payment-beneficiaries-delete'),
]

