"""
Django signals for bill payment notifications.

This module handles sending notifications when bill payment request statuses change.
"""

from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.db import transaction

from bowenmfb.modules.utils import send_notification_async, log_request, create_in_app_notification
from .models import BillPaymentRequest


@receiver(pre_save, sender=BillPaymentRequest)
def track_bill_payment_status_changes(sender, instance, **kwargs):
    """Track status changes for bill payment requests before saving."""
    if instance.pk:
        try:
            # Get the previous state from database
            old_instance = BillPaymentRequest.objects.get(pk=instance.pk)
            instance._old_is_checked = old_instance.is_checked
            instance._old_is_verified = old_instance.is_verified
            instance._old_is_approved = old_instance.is_approved
            instance._old_is_declined = old_instance.is_declined
        except BillPaymentRequest.DoesNotExist:
            instance._old_is_checked = False
            instance._old_is_verified = False
            instance._old_is_approved = False
            instance._old_is_declined = False
    else:
        instance._old_is_checked = False
        instance._old_is_verified = False
        instance._old_is_approved = False
        instance._old_is_declined = False


@receiver(post_save, sender=BillPaymentRequest)
def send_bill_payment_notifications(sender, instance, created, **kwargs):
    """Send notifications when bill payment request status changes."""
    if created:
        # Create in-app notification for new bill payment request
        if instance.company:
            biller_name = instance.biller.name if instance.biller else "Unknown Biller"
            transaction.on_commit(lambda: create_in_app_notification(
                company=instance.company,
                title="New Bill Payment Request Created",
                message=f"A new bill payment request of ₦{instance.amount:,.2f} to {biller_name} for {instance.customer_name} has been created.",
                notification_type='bill_payment_created',
                related_object_id=instance.id,
                related_object_type='BillPaymentRequest'
            ))
        return
    
    # Check if any status has changed
    status_changes = []
    
    if hasattr(instance, '_old_is_checked') and not instance._old_is_checked and instance.is_checked:
        status_changes.append('checked')
    
    if hasattr(instance, '_old_is_verified') and not instance._old_is_verified and instance.is_verified:
        status_changes.append('verified')
    
    if hasattr(instance, '_old_is_approved') and not instance._old_is_approved and instance.is_approved:
        status_changes.append('approved')
    
    if hasattr(instance, '_old_is_declined') and not instance._old_is_declined and instance.is_declined:
        status_changes.append('declined')
    
    # Send notifications for each status change
    for status in status_changes:
        transaction.on_commit(lambda s=status: send_bill_payment_status_notification(instance, s))


def send_bill_payment_status_notification(bill_payment_request, status):
    """Send notification for bill payment status change."""
    try:
        if not bill_payment_request.created_by:
            return
        
        customer = bill_payment_request.created_by
        company_name = bill_payment_request.company.name if bill_payment_request.company else "Your Company"
        biller_name = bill_payment_request.biller.name if bill_payment_request.biller else "Unknown Biller"
        
        # Prepare status-specific messages
        status_messages = {
            'checked': {
                'sms': f"Your bill payment of ₦{bill_payment_request.amount:,.2f} to {biller_name} for {bill_payment_request.customer_name} has been checked and is under review.",
                'email_subject': 'Bill Payment Request Checked',
                'email_body': f"""
                <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h2 style="color: #2c5aa0; margin-bottom: 20px;">Bill Payment Request Checked</h2>

                            <p>Dear <strong>{customer.user.get_full_name()}</strong>,</p>

                            <p>Your bill payment request has been checked and is now under review.</p>

                            <div style="background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
                                <h3 style="color: #856404; margin-top: 0;">Payment Details</h3>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold; width: 30%;">Amount:</td>
                                        <td style="padding: 8px 0; color: #2c5aa0; font-weight: bold;">₦{bill_payment_request.amount:,.2f}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Biller:</td>
                                        <td style="padding: 8px 0;">{biller_name}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Customer ID:</td>
                                        <td style="padding: 8px 0;">{bill_payment_request.customer_id}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Customer Name:</td>
                                        <td style="padding: 8px 0;">{bill_payment_request.customer_name}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Description:</td>
                                        <td style="padding: 8px 0;">{bill_payment_request.description or 'N/A'}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Company:</td>
                                        <td style="padding: 8px 0;">{company_name}</td>
                                    </tr>
                                </table>
                            </div>

                            <p><strong>Status:</strong> Your request is now in the verification stage.</p>

                            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                            <p style="color: #666; font-size: 14px;">
                                Best regards,<br>
                                <strong>Bowen MFB Team</strong>
                            </p>
                        </div>
                    </div>
                </body>
                </html>
                """
            },
            'verified': {
                'sms': f"Your bill payment of ₦{bill_payment_request.amount:,.2f} to {biller_name} for {bill_payment_request.customer_name} has been verified and is awaiting final approval.",
                'email_subject': 'Bill Payment Request Verified',
                'email_body': f"""
                <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h2 style="color: #007bff; margin-bottom: 20px;">Bill Payment Request Verified</h2>

                            <p>Dear <strong>{customer.user.get_full_name()}</strong>,</p>

                            <p>Your bill payment request has been verified and is awaiting final approval.</p>

                            <div style="background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #007bff;">
                                <h3 style="color: #004085; margin-top: 0;">Payment Details</h3>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold; width: 30%;">Amount:</td>
                                        <td style="padding: 8px 0; color: #007bff; font-weight: bold;">₦{bill_payment_request.amount:,.2f}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Biller:</td>
                                        <td style="padding: 8px 0;">{biller_name}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Customer ID:</td>
                                        <td style="padding: 8px 0;">{bill_payment_request.customer_id}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Customer Name:</td>
                                        <td style="padding: 8px 0;">{bill_payment_request.customer_name}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Description:</td>
                                        <td style="padding: 8px 0;">{bill_payment_request.description or 'N/A'}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Company:</td>
                                        <td style="padding: 8px 0;">{company_name}</td>
                                    </tr>
                                </table>
                            </div>

                            <p><strong>Status:</strong> Your request is now in the final approval stage.</p>

                            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                            <p style="color: #666; font-size: 14px;">
                                Best regards,<br>
                                <strong>Bowen MFB Team</strong>
                            </p>
                        </div>
                    </div>
                </body>
                </html>
                """
            },
            'approved': {
                'sms': f"Great news! Your bill payment of ₦{bill_payment_request.amount:,.2f} to {biller_name} for {bill_payment_request.customer_name} has been approved and will be processed shortly.",
                'email_subject': 'Bill Payment Request Approved',
                'email_body': f"""
                <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h2 style="color: #28a745; margin-bottom: 20px;">✅ Bill Payment Request Approved</h2>

                            <p>Dear <strong>{customer.user.get_full_name()}</strong>,</p>

                            <p><strong>Great news!</strong> Your bill payment request has been approved and will be processed shortly.</p>

                            <div style="background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;">
                                <h3 style="color: #155724; margin-top: 0;">Payment Details</h3>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold; width: 30%;">Amount:</td>
                                        <td style="padding: 8px 0; color: #28a745; font-weight: bold; font-size: 18px;">₦{bill_payment_request.amount:,.2f}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Biller:</td>
                                        <td style="padding: 8px 0;">{biller_name}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Customer ID:</td>
                                        <td style="padding: 8px 0;">{bill_payment_request.customer_id}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Customer Name:</td>
                                        <td style="padding: 8px 0;">{bill_payment_request.customer_name}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Description:</td>
                                        <td style="padding: 8px 0;">{bill_payment_request.description or 'N/A'}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Company:</td>
                                        <td style="padding: 8px 0;">{company_name}</td>
                                    </tr>
                                </table>
                            </div>

                            <p>The payment will be executed and you will receive a confirmation once completed.</p>

                            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                            <p style="color: #666; font-size: 14px;">
                                Best regards,<br>
                                <strong>Bowen MFB Team</strong>
                            </p>
                        </div>
                    </div>
                </body>
                </html>
                """
            },
            'declined': {
                'sms': f"Your bill payment of ₦{bill_payment_request.amount:,.2f} to {biller_name} for {bill_payment_request.customer_name} has been declined. Reason: {bill_payment_request.decline_reason or 'Not specified'}",
                'email_subject': 'Bill Payment Request Declined',
                'email_body': f"""
                <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h2 style="color: #dc3545; margin-bottom: 20px;">❌ Bill Payment Request Declined</h2>

                            <p>Dear <strong>{customer.user.get_full_name()}</strong>,</p>

                            <p>We regret to inform you that your bill payment request has been declined.</p>

                            <div style="background-color: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #dc3545;">
                                <h3 style="color: #721c24; margin-top: 0;">Payment Details</h3>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold; width: 30%;">Amount:</td>
                                        <td style="padding: 8px 0; color: #dc3545; font-weight: bold;">₦{bill_payment_request.amount:,.2f}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Biller:</td>
                                        <td style="padding: 8px 0;">{biller_name}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Customer ID:</td>
                                        <td style="padding: 8px 0;">{bill_payment_request.customer_id}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Customer Name:</td>
                                        <td style="padding: 8px 0;">{bill_payment_request.customer_name}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Description:</td>
                                        <td style="padding: 8px 0;">{bill_payment_request.description or 'N/A'}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Company:</td>
                                        <td style="padding: 8px 0;">{company_name}</td>
                                    </tr>
                                </table>
                            </div>

                            <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
                                <p style="margin: 0; color: #856404;">
                                    <strong>Reason for decline:</strong> {bill_payment_request.decline_reason or 'Not specified'}
                                </p>
                            </div>

                            <p>Please contact your account officer for more information or to submit a new request.</p>

                            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                            <p style="color: #666; font-size: 14px;">
                                Best regards,<br>
                                <strong>Bowen MFB Team</strong>
                            </p>
                        </div>
                    </div>
                </body>
                </html>
                """
            }
        }
        
        if status in status_messages:
            message_data = status_messages[status]
            
            # Send SMS notification
            send_notification_async(
                notification_type='sms',
                recipient=customer.phone_number,
                message=message_data['sms']
            )
            
            # Send email notification
            send_notification_async(
                notification_type='email',
                recipient=customer.user.email,
                message=message_data['email_body'],
                subject=message_data['email_subject']
            )
            
            log_request(f"Sent {status} notification for bill payment {bill_payment_request.id} to {customer.user.email}")

            # Create in-app notification
            if bill_payment_request.company:
                notification_titles = {
                    'checked': 'Bill Payment Request Checked',
                    'verified': 'Bill Payment Request Verified',
                    'approved': 'Bill Payment Request Approved',
                    'declined': 'Bill Payment Request Declined'
                }

                notification_messages = {
                    'checked': f"Your bill payment of ₦{bill_payment_request.amount:,.2f} to {biller_name} for {bill_payment_request.customer_name} has been checked.",
                    'verified': f"Your bill payment of ₦{bill_payment_request.amount:,.2f} to {biller_name} for {bill_payment_request.customer_name} has been verified.",
                    'approved': f"Your bill payment of ₦{bill_payment_request.amount:,.2f} to {biller_name} for {bill_payment_request.customer_name} has been approved.",
                    'declined': f"Your bill payment of ₦{bill_payment_request.amount:,.2f} to {biller_name} for {bill_payment_request.customer_name} has been declined."
                }

                create_in_app_notification(
                    company=bill_payment_request.company,
                    title=notification_titles.get(status, 'Bill Payment Status Update'),
                    message=notification_messages.get(status, f'Bill payment status updated to {status}'),
                    notification_type=f'bill_payment_{status}',
                    related_object_id=bill_payment_request.id,
                    related_object_type='BillPaymentRequest'
                )

    except Exception as e:
        log_request(f"Failed to send bill payment {status} notification: {str(e)}")
