from django.contrib import admin
from .models import (
    BankList, TransferBeneficiary, TransferScheduler,
    SingleTransferRequest, SingleTransfer, BulkTransferRequest, BulkTransfer, AccountSignatory, TransferApprovalWorkflow
)


@admin.register(BankList)
class BankListAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'created_at']
    search_fields = ['name', 'code']
    readonly_fields = ['id', 'created_at', 'updated_at']


@admin.register(TransferBeneficiary)
class TransferBeneficiaryAdmin(admin.ModelAdmin):
    list_display = ['name', 'get_company_name', 'beneficiary_type', 'bank_name', 'account_number']
    list_filter = ['beneficiary_type', 'bank_name', 'created_at']
    search_fields = ['name', 'company__name', 'bank_name', 'account_number']
    readonly_fields = ['id', 'created_at', 'updated_at']

    def get_company_name(self, obj):
        return obj.company.name
    get_company_name.short_description = "Company"


@admin.register(SingleTransferRequest)
class SingleTransferRequestAdmin(admin.ModelAdmin):
    list_display = ['get_company_name', 'beneficiary_name', 'amount', 'is_checked',
                   'is_verified', 'is_approved', 'is_declined', 'created_at']
    list_filter = ['is_checked', 'is_verified', 'is_approved', 'is_declined', 'created_at']
    search_fields = ['company__name', 'beneficiary_name', 'beneficiary_account_number']
    readonly_fields = ['id', 'created_at', 'updated_at']

    def get_company_name(self, obj):
        return obj.company.name if obj.company else "No Company"
    get_company_name.short_description = "Company"


@admin.register(BulkTransferRequest)
class BulkTransferRequestAdmin(admin.ModelAdmin):
    list_display = ['get_company_name', 'is_checked', 'is_verified', 'is_approved', 'is_declined', 'created_at']
    list_filter = ['is_checked', 'is_verified', 'is_approved', 'is_declined', 'created_at']
    search_fields = ['company__name', 'description']
    readonly_fields = ['id', 'created_at', 'updated_at']

    def get_company_name(self, obj):
        return obj.company.name if obj.company else "No Company"
    get_company_name.short_description = "Company"


@admin.register(BulkTransfer)
class BulkTransferAdmin(admin.ModelAdmin):
    list_display = ['get_bulk_request', 'beneficiary_name', 'amount', 'status', 'created_at']
    list_filter = ['status', 'transfer_type', 'created_at']
    search_fields = ['beneficiary_name', 'beneficiary_acct_number', 'bulk_request__description']
    readonly_fields = ['id', 'created_at', 'updated_at']

    def get_bulk_request(self, obj):
        return f"Bulk #{obj.bulk_request.id}"
    get_bulk_request.short_description = "Bulk Request"


@admin.register(SingleTransfer)
class SingleTransferAdmin(admin.ModelAdmin):
    list_display = ['get_transfer_request', 'beneficiary_name', 'amount', 'status', 'created_at']
    list_filter = ['status', 'transfer_type', 'created_at']
    search_fields = ['beneficiary_name', 'beneficiary_acct_number', 'transfer_request__description']
    readonly_fields = ['id', 'created_at', 'updated_at']

    def get_transfer_request(self, obj):
        return f"Single #{obj.transfer_request.id}"
    get_transfer_request.short_description = "Single Request"


@admin.register(AccountSignatory)
class AccountSignatoryAdmin(admin.ModelAdmin):
    list_display = ['get_account_signatories', 'company_account', 'customer', 'role', 'is_active']
    list_filter = ['can_upload', 'can_check', 'can_verify', 'can_approve']
    search_fields = ['customer']

    def get_account_signatories(self, obj):
        return f"Signatory #{obj.customer.user.username}"
    get_account_signatories.short_description = "Account Signatories"


@admin.register(TransferApprovalWorkflow)
class TransferApprovalWorkflowAdmin(admin.ModelAdmin):
    list_display = ['single_transfer_request', 'bulk_transfer_request', 'status']
    list_filter = ['status']


@admin.register(TransferScheduler)
class TransferSchedulerAdmin(admin.ModelAdmin):
    list_display = ['schedule_type', 'get_company_name', 'status', 'category', 'completed']
    list_filter = ['schedule_type', 'status', 'category', 'completed']
    search_fields = ['company__name']
    readonly_fields = ['id', 'created_at', 'updated_at']

    def get_company_name(self, obj):
        if obj.company:
            return obj.company.name
        return "No Company"
    get_company_name.short_description = "Company"
