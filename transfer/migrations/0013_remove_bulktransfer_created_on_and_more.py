# Generated by Django 5.2.1 on 2025-07-23 13:15

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('transfer', '0012_remove_accountsignatory_hierarchy_level'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='bulktransfer',
            name='created_on',
        ),
        migrations.RemoveField(
            model_name='bulktransfer',
            name='updated_on',
        ),
        migrations.RemoveField(
            model_name='bulktransferrequest',
            name='created_on',
        ),
        migrations.RemoveField(
            model_name='bulktransferrequest',
            name='updated_on',
        ),
        migrations.RemoveField(
            model_name='singletransfer',
            name='created_on',
        ),
        migrations.RemoveField(
            model_name='singletransfer',
            name='updated_on',
        ),
        migrations.RemoveField(
            model_name='singletransferrequest',
            name='created_on',
        ),
        migrations.RemoveField(
            model_name='singletransferrequest',
            name='updated_on',
        ),
        migrations.RemoveField(
            model_name='transferscheduler',
            name='created_on',
        ),
        migrations.RemoveField(
            model_name='transferscheduler',
            name='updated_on',
        ),
    ]
