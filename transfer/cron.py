import logging
from datetime import timedelta
from dateutil.relativedelta import relativedelta
from django.utils import timezone
from django.db import transaction

from .models import TransferScheduler, SingleTransferRequest, BulkTransferRequest, TransferApprovalWorkflow
from .tasks import perform_bowen_fund_transfer, create_bulk_transfer
from bowenmfb.modules.utils import log_request

logger = logging.getLogger(__name__)


def process_scheduled_transfers():
    """
    Cron job function to process active scheduled transfers.

    This function:
    1. Finds active scheduled transfers that are due for execution
    2. Gets the associated transfer requests
    3. Processes the transfers if they are approved
    4. Updates the scheduler's timing information
    """
    try:
        now = timezone.now()

        # Get active schedulers that are due for execution
        due_schedulers = TransferScheduler.objects.filter(
            status="active",
            completed=False,
            next_job_date__lte=now
        ).select_related('company')

        log_request(f"Found {due_schedulers.count()} scheduled transfers due for processing")

        for scheduler in due_schedulers:
            try:
                with transaction.atomic():
                    process_single_scheduler(scheduler, now)
            except Exception as e:
                logger.error(f"Error processing scheduler {scheduler.id}: {str(e)}")
                log_request(f"Error processing scheduler {scheduler.id}: {str(e)}")
                continue

        log_request("Completed processing scheduled transfers")

    except Exception as e:
        logger.error(f"Error in process_scheduled_transfers: {str(e)}")
        log_request(f"Error in process_scheduled_transfers: {str(e)}")


def process_single_scheduler(scheduler, current_time):
    """
    Process a single scheduler instance.

    Args:
        scheduler: TransferScheduler instance
        current_time: Current datetime
    """
    log_request(f"Processing scheduler {scheduler.id} for company {scheduler.company.name}")

    # Find associated transfer requests
    single_requests = SingleTransferRequest.objects.filter(scheduler=scheduler, scheduled=True)
    bulk_requests = BulkTransferRequest.objects.filter(scheduler=scheduler, scheduled=True)

    processed_any = False

    # Process single transfer requests
    for single_request in single_requests:
        if process_single_transfer_request(single_request):
            processed_any = True

    # Process bulk transfer requests
    for bulk_request in bulk_requests:
        if process_bulk_transfer_request(bulk_request):
            processed_any = True

    if processed_any:
        # Update scheduler timing
        scheduler.last_job_date = current_time

        # Use the model's method to update next job date
        scheduler.update_next_job_date()

        log_request(f"Updated scheduler {scheduler.id} - next run: {scheduler.next_job_date}")


def process_single_transfer_request(single_request):
    """
    Process a single transfer request if it's approved.

    Args:
        single_request: SingleTransferRequest instance

    Returns:
        bool: True if processed, False otherwise
    """
    try:
        # Check if there's an approved workflow for this request
        workflow = TransferApprovalWorkflow.objects.filter(
            single_transfer_request=single_request,
            status="approved"
        ).first()

        if workflow:
            log_request(f"Processing approved single transfer request {single_request.id}")
            # Call the existing task to perform the transfer
            perform_bowen_fund_transfer.delay(workflow.id)
            return True
        else:
            log_request(f"Single transfer request {single_request.id} not yet approved, skipping")
            return False

    except Exception as e:
        logger.error(f"Error processing single transfer request {single_request.id}: {str(e)}")
        log_request(f"Error processing single transfer request {single_request.id}: {str(e)}")
        return False


def process_bulk_transfer_request(bulk_request):
    """
    Process a bulk transfer request if it's approved.

    Args:
        bulk_request: BulkTransferRequest instance

    Returns:
        bool: True if processed, False otherwise
    """
    try:
        # Check if there's an approved workflow for this request
        workflow = TransferApprovalWorkflow.objects.filter(
            bulk_transfer_request=bulk_request,
            status="approved"
        ).first()

        if workflow:
            log_request(f"Processing approved bulk transfer request {bulk_request.id}")
            # Call the existing task to create and process bulk transfers
            create_bulk_transfer.delay(bulk_request.id, "false")
            return True
        else:
            log_request(f"Bulk transfer request {bulk_request.id} not yet approved, skipping")
            return False

    except Exception as e:
        logger.error(f"Error processing bulk transfer request {bulk_request.id}: {str(e)}")
        log_request(f"Error processing bulk transfer request {bulk_request.id}: {str(e)}")
        return False


def calculate_next_job_date(scheduler, current_time):
    """
    Calculate the next job date based on schedule type.

    Args:
        scheduler: TransferScheduler instance
        current_time: Current datetime

    Returns:
        datetime or None: Next execution time or None if no next execution
    """
    schedule_type = scheduler.schedule_type

    if schedule_type == "once":
        return None  # One-time execution, no next date

    elif schedule_type == "daily":
        return current_time + timedelta(days=1)

    elif schedule_type == "weekly":
        # Schedule for the same day of week next week
        days_ahead = int(scheduler.day_of_the_week) - current_time.weekday() - 1
        if days_ahead <= 0:  # Target day already happened this week
            days_ahead += 7
        return current_time + timedelta(days=days_ahead)

    elif schedule_type == "monthly":
        # Schedule for the same day of month next month
        day_of_month = int(scheduler.day_of_the_month)
        next_month = current_time + relativedelta(months=1)
        try:
            return next_month.replace(day=day_of_month)
        except ValueError:
            # Handle cases where day doesn't exist in next month (e.g., Feb 30)
            return next_month.replace(day=min(day_of_month, 28))

    elif schedule_type == "quarterly":
        # Schedule for 3 months later
        day_of_month = int(scheduler.day_of_the_month)
        next_quarter = current_time + relativedelta(months=3)
        try:
            return next_quarter.replace(day=day_of_month)
        except ValueError:
            return next_quarter.replace(day=min(day_of_month, 28))

    elif schedule_type == "bi-annually":
        # Schedule for 6 months later
        day_of_month = int(scheduler.day_of_the_month)
        next_period = current_time + relativedelta(months=6)
        try:
            return next_period.replace(day=day_of_month)
        except ValueError:
            return next_period.replace(day=min(day_of_month, 28))

    elif schedule_type == "yearly":
        # Schedule for same date next year
        day_of_month = int(scheduler.day_of_the_month)
        next_year = current_time + relativedelta(years=1)
        try:
            return next_year.replace(day=day_of_month)
        except ValueError:
            return next_year.replace(day=min(day_of_month, 28))

    else:
        logger.warning(f"Unknown schedule type: {schedule_type}")
        return None


