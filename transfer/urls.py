from django.urls import path
from . import views


app_name = "transfer"

urlpatterns = [
    path('banks', views.ListBankGenericAPIView.as_view(), name="banks"),
    path('name-enquiry', views.NameEnquiryAPIView.as_view(), name="name-enquiry"),
    path('balance-enquiry', views.AccountBalanceAPIView.as_view(), name="balance-enquiry"),
    path('history', views.TransactionHistoryAPIView.as_view(), name="history"),

    # Transfer Approval Workflow
    path('transfer-approval', views.TransferApprovalWorkflowAPIView.as_view(), name="transfer-workflow"),
    path('transfer-approval/process', views.ProcessTransferApprovalAPIView.as_view(), name="process-transfer-approval"),

    # Bulk Transfer Template
    path('bulk-template', views.BulkTransferTemplateDownloadAPIView.as_view(), name="bulk-transfer-template"),

    # Single Transfer
    path('single-transfer-request', views.CreateSingleTransferRequestAPIView.as_view(), name="single-transfer-request"),
    path('single-transfer-request/list', views.ListSingleTransferRequest.as_view(), name="single-transfer-request-list"),
    path('single-transfer-request/list/<uuid:id>', views.ListSingleTransferRequest.as_view(), name="single-transfer-request-list-detail"),
    path('single-transfer', views.SingleTransferListAPIView.as_view(), name="single-transfer"),
    path('single-transfer/<uuid:id>', views.SingleTransferListAPIView.as_view(), name="single-transfer-detail"),

    # Bulk Transfer
    path('bulk-transfer-request/create', views.BulkTransferRequestAPIView.as_view(), name="bulk-transfer-request"),
    path('bulk-transfer-request/list', views.BulkTransferRequestListAPIView.as_view(), name="bulk-transfer-request-list"),
    path('bulk-transfer-request/list/<uuid:id>', views.BulkTransferRequestListAPIView.as_view(), name="bulk-transfer-request-list-detail"),
    path('bulk-transfer', views.BulkTransferListAPIView.as_view(), name="bulk-transfer"),
    path('bulk-transfer/<uuid:id>', views.BulkTransferListAPIView.as_view(), name="bulk-transfer-detail"),

    # Beneficiaries
    path('transfer-beneficiary/add', views.AddTransferBeneficiaryAPIView.as_view(), name="transfer-beneficiary-add"),
    path('transfer-beneficiary/list', views.TransferBeneficiaryListAPIView.as_view(), name="transfer-beneficiary-list"),
    path('transfer-beneficiary/list/<uuid:id>', views.TransferBeneficiaryListAPIView.as_view(), name="transfer-beneficiary-list-detail"),
    path('transfer-beneficiary/delete/<uuid:id>', views.DeleteTransferBeneficiary.as_view(), name="transfer-beneficiary-delete"),

    # Statement Download
    path('download-statement', views.StatementDownloadAPIView.as_view(), name="download-statement"),

    # Transfer Scheduler
    path('transfer-scheduler', views.ScheduledTransferListAPIView.as_view(), name="transfer-scheduler"),
    path('transfer-scheduler/<uuid:id>', views.ScheduledTransferListAPIView.as_view(), name="transfer-scheduler-detail"),
    path('transfer-scheduler/<uuid:id>/edit', views.EditTransferSchedulerAPIView.as_view(), name="transfer-scheduler-edit"),

    # Company Signatory List
    path('signatories', views.ListCompanyAccountSignatoriesAPIView.as_view(), name="account-signatories"),
    path('signatories/<uuid:id>', views.ListCompanyAccountSignatoriesAPIView.as_view(), name="account-signatories-detail"),


]

