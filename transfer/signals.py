from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.db import transaction

from bowenmfb.modules.utils import send_notification_async, log_request, create_in_app_notification
from .models import SignatoryHierarchy, CompanyAccount, Customer, AccountSignatory, SingleTransferRequest, BulkTransferRequest


# @receiver(signal=post_save, sender=CompanyAccount)
# def create_account_hierarchy(sender, instance, **kwargs):
#     company = instance.company
#     hierarchy, _ = SignatoryHierarchy.objects.get_or_create(company_account=instance)
#     signatories = Customer.objects.filter(company=company)
#     for customer in signatories:
#         account_signatory, _ = AccountSignatory.objects.get_or_create(company_account=instance, customer=customer)
#     total_company_signatories = len(signatories)
#     hierarchy.total_levels = total_company_signatories
#     if total_company_signatories > 1:
#         hierarchy.is_single_signatory = False
#         hierarchy.requires_approver = True
#         hierarchy.requires_verifier = True
#         hierarchy.requires_checker = True
#     hierarchy.save()
#
#     return True


# Transfer notification signals

@receiver(pre_save, sender=SingleTransferRequest)
def track_single_transfer_status_changes(sender, instance, **kwargs):
    """Track status changes for single transfer requests before saving."""
    if instance.pk:
        try:
            # Get the previous state from database
            old_instance = SingleTransferRequest.objects.get(pk=instance.pk)
            instance._old_is_checked = old_instance.is_checked
            instance._old_is_verified = old_instance.is_verified
            instance._old_is_approved = old_instance.is_approved
            instance._old_is_declined = old_instance.is_declined
        except SingleTransferRequest.DoesNotExist:
            instance._old_is_checked = False
            instance._old_is_verified = False
            instance._old_is_approved = False
            instance._old_is_declined = False
    else:
        instance._old_is_checked = False
        instance._old_is_verified = False
        instance._old_is_approved = False
        instance._old_is_declined = False


@receiver(post_save, sender=SingleTransferRequest)
def send_single_transfer_notifications(sender, instance, created, **kwargs):
    """Send notifications when single transfer request status changes."""
    if created:
        # Create in-app notification for new transfer request
        if instance.company:
            transaction.on_commit(lambda: create_in_app_notification(
                company=instance.company,
                title="New Transfer Request Created",
                message=f"A new transfer request of ₦{instance.amount:,.2f} to {instance.beneficiary_name} has been created.",
                notification_type='transfer_created',
                related_object_id=instance.id,
                related_object_type='SingleTransferRequest'
            ))
        return

    # Check if any status has changed
    status_changes = []

    if hasattr(instance, '_old_is_checked') and not instance._old_is_checked and instance.is_checked:
        status_changes.append('checked')

    if hasattr(instance, '_old_is_verified') and not instance._old_is_verified and instance.is_verified:
        status_changes.append('verified')

    if hasattr(instance, '_old_is_approved') and not instance._old_is_approved and instance.is_approved:
        status_changes.append('approved')

    if hasattr(instance, '_old_is_declined') and not instance._old_is_declined and instance.is_declined:
        status_changes.append('declined')

    # Send notifications for each status change
    for status in status_changes:
        transaction.on_commit(lambda s=status: send_single_transfer_status_notification(instance, s))


def send_single_transfer_status_notification(transfer_request, status):
    """Send notification for single transfer status change."""
    try:
        if not transfer_request.created_by:
            return

        customer = transfer_request.created_by
        company_name = transfer_request.company.name if transfer_request.company else "Your Company"

        # Prepare status-specific messages
        status_messages = {
            'checked': {
                'sms': f"Your transfer request of ₦{transfer_request.amount:,.2f} to {transfer_request.beneficiary_name} has been checked and is now under review.",
                'email_subject': 'Transfer Request Checked',
                'email_body': f"""
                <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h2 style="color: #2c5aa0; margin-bottom: 20px;">Transfer Request Checked</h2>

                            <p>Dear <strong>{customer.user.get_full_name()}</strong>,</p>

                            <p>Your transfer request has been checked and is now under review.</p>

                            <div style="background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
                                <h3 style="color: #856404; margin-top: 0;">Transfer Details</h3>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold; width: 30%;">Amount:</td>
                                        <td style="padding: 8px 0; color: #2c5aa0; font-weight: bold;">₦{transfer_request.amount:,.2f}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Beneficiary:</td>
                                        <td style="padding: 8px 0;">{transfer_request.beneficiary_name}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Account Number:</td>
                                        <td style="padding: 8px 0;">{transfer_request.beneficiary_account_number}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Bank:</td>
                                        <td style="padding: 8px 0;">{transfer_request.bank_name or 'N/A'}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Description:</td>
                                        <td style="padding: 8px 0;">{transfer_request.description}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Company:</td>
                                        <td style="padding: 8px 0;">{company_name}</td>
                                    </tr>
                                </table>
                            </div>

                            <p><strong>Status:</strong> Your request is now in the verification stage.</p>

                            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                            <p style="color: #666; font-size: 14px;">
                                Best regards,<br>
                                <strong>Bowen MFB Team</strong>
                            </p>
                        </div>
                    </div>
                </body>
                </html>
                """
            },
            'verified': {
                'sms': f"Your transfer request of ₦{transfer_request.amount:,.2f} to {transfer_request.beneficiary_name} has been verified and is awaiting final approval.",
                'email_subject': 'Transfer Request Verified',
                'email_body': f"""
                <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h2 style="color: #2c5aa0; margin-bottom: 20px;">Transfer Request Verified</h2>

                            <p>Dear <strong>{customer.user.get_full_name()}</strong>,</p>

                            <p>Your transfer request has been verified and is awaiting final approval.</p>

                            <div style="background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #007bff;">
                                <h3 style="color: #004085; margin-top: 0;">Transfer Details</h3>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold; width: 30%;">Amount:</td>
                                        <td style="padding: 8px 0; color: #2c5aa0; font-weight: bold;">₦{transfer_request.amount:,.2f}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Beneficiary:</td>
                                        <td style="padding: 8px 0;">{transfer_request.beneficiary_name}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Account Number:</td>
                                        <td style="padding: 8px 0;">{transfer_request.beneficiary_account_number}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Bank:</td>
                                        <td style="padding: 8px 0;">{transfer_request.bank_name or 'N/A'}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Description:</td>
                                        <td style="padding: 8px 0;">{transfer_request.description}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Company:</td>
                                        <td style="padding: 8px 0;">{company_name}</td>
                                    </tr>
                                </table>
                            </div>

                            <p><strong>Status:</strong> Your request is now in the final approval stage.</p>

                            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                            <p style="color: #666; font-size: 14px;">
                                Best regards,<br>
                                <strong>Bowen MFB Team</strong>
                            </p>
                        </div>
                    </div>
                </body>
                </html>
                """
            },
            'approved': {
                'sms': f"Great news! Your transfer request of ₦{transfer_request.amount:,.2f} to {transfer_request.beneficiary_name} has been approved and will be processed shortly.",
                'email_subject': 'Transfer Request Approved',
                'email_body': f"""
                <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h2 style="color: #28a745; margin-bottom: 20px;">✅ Transfer Request Approved</h2>

                            <p>Dear <strong>{customer.user.get_full_name()}</strong>,</p>

                            <p><strong>Great news!</strong> Your transfer request has been approved and will be processed shortly.</p>

                            <div style="background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;">
                                <h3 style="color: #155724; margin-top: 0;">Transfer Details</h3>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold; width: 30%;">Amount:</td>
                                        <td style="padding: 8px 0; color: #28a745; font-weight: bold; font-size: 18px;">₦{transfer_request.amount:,.2f}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Beneficiary:</td>
                                        <td style="padding: 8px 0;">{transfer_request.beneficiary_name}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Account Number:</td>
                                        <td style="padding: 8px 0;">{transfer_request.beneficiary_account_number}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Bank:</td>
                                        <td style="padding: 8px 0;">{transfer_request.bank_name or 'N/A'}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Description:</td>
                                        <td style="padding: 8px 0;">{transfer_request.description}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Company:</td>
                                        <td style="padding: 8px 0;">{company_name}</td>
                                    </tr>
                                </table>
                            </div>

                            <p>The transfer will be executed and you will receive a confirmation once completed.</p>

                            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                            <p style="color: #666; font-size: 14px;">
                                Best regards,<br>
                                <strong>Bowen MFB Team</strong>
                            </p>
                        </div>
                    </div>
                </body>
                </html>
                """
            },
            'declined': {
                'sms': f"Your transfer request of ₦{transfer_request.amount:,.2f} to {transfer_request.beneficiary_name} has been declined. Reason: {transfer_request.decline_reason or 'Not specified'}",
                'email_subject': 'Transfer Request Declined',
                'email_body': f"""
                <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h2 style="color: #dc3545; margin-bottom: 20px;">❌ Transfer Request Declined</h2>

                            <p>Dear <strong>{customer.user.get_full_name()}</strong>,</p>

                            <p>We regret to inform you that your transfer request has been declined.</p>

                            <div style="background-color: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #dc3545;">
                                <h3 style="color: #721c24; margin-top: 0;">Transfer Details</h3>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold; width: 30%;">Amount:</td>
                                        <td style="padding: 8px 0; color: #dc3545; font-weight: bold;">₦{transfer_request.amount:,.2f}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Beneficiary:</td>
                                        <td style="padding: 8px 0;">{transfer_request.beneficiary_name}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Account Number:</td>
                                        <td style="padding: 8px 0;">{transfer_request.beneficiary_account_number}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Bank:</td>
                                        <td style="padding: 8px 0;">{transfer_request.bank_name or 'N/A'}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Description:</td>
                                        <td style="padding: 8px 0;">{transfer_request.description}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Company:</td>
                                        <td style="padding: 8px 0;">{company_name}</td>
                                    </tr>
                                </table>
                            </div>

                            <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
                                <p style="margin: 0; color: #856404;">
                                    <strong>Reason for decline:</strong> {transfer_request.decline_reason or 'Not specified'}
                                </p>
                            </div>

                            <p>Please contact your account officer for more information or to submit a new request.</p>

                            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                            <p style="color: #666; font-size: 14px;">
                                Best regards,<br>
                                <strong>Bowen MFB Team</strong>
                            </p>
                        </div>
                    </div>
                </body>
                </html>
                """
            }
        }

        if status in status_messages:
            message_data = status_messages[status]

            # Send SMS notification
            send_notification_async(
                notification_type='sms',
                recipient=customer.phone_number,
                message=message_data['sms']
            )

            # Send email notification
            send_notification_async(
                notification_type='email',
                recipient=customer.user.email,
                message=message_data['email_body'],
                subject=message_data['email_subject']
            )

            log_request(f"Sent {status} notification for single transfer {transfer_request.id} to {customer.user.email}")

            # Create in-app notification
            if transfer_request.company:
                notification_titles = {
                    'checked': 'Transfer Request Checked',
                    'verified': 'Transfer Request Verified',
                    'approved': 'Transfer Request Approved',
                    'declined': 'Transfer Request Declined'
                }

                notification_messages = {
                    'checked': f"Your transfer request of ₦{transfer_request.amount:,.2f} to {transfer_request.beneficiary_name} has been checked.",
                    'verified': f"Your transfer request of ₦{transfer_request.amount:,.2f} to {transfer_request.beneficiary_name} has been verified.",
                    'approved': f"Your transfer request of ₦{transfer_request.amount:,.2f} to {transfer_request.beneficiary_name} has been approved.",
                    'declined': f"Your transfer request of ₦{transfer_request.amount:,.2f} to {transfer_request.beneficiary_name} has been declined."
                }

                create_in_app_notification(
                    company=transfer_request.company,
                    title=notification_titles.get(status, 'Transfer Status Update'),
                    message=notification_messages.get(status, f'Transfer status updated to {status}'),
                    notification_type=f'transfer_{status}',
                    related_object_id=transfer_request.id,
                    related_object_type='SingleTransferRequest'
                )

    except Exception as e:
        log_request(f"Failed to send single transfer {status} notification: {str(e)}")


@receiver(pre_save, sender=BulkTransferRequest)
def track_bulk_transfer_status_changes(sender, instance, **kwargs):
    """Track status changes for bulk transfer requests before saving."""
    if instance.pk:
        try:
            # Get the previous state from database
            old_instance = BulkTransferRequest.objects.get(pk=instance.pk)
            instance._old_is_checked = old_instance.is_checked
            instance._old_is_verified = old_instance.is_verified
            instance._old_is_approved = old_instance.is_approved
            instance._old_is_declined = old_instance.is_declined
        except BulkTransferRequest.DoesNotExist:
            instance._old_is_checked = False
            instance._old_is_verified = False
            instance._old_is_approved = False
            instance._old_is_declined = False
    else:
        instance._old_is_checked = False
        instance._old_is_verified = False
        instance._old_is_approved = False
        instance._old_is_declined = False


@receiver(post_save, sender=BulkTransferRequest)
def send_bulk_transfer_notifications(sender, instance, created, **kwargs):
    """Send notifications when bulk transfer request status changes."""
    if created:
        # Create in-app notification for new bulk transfer request
        if instance.company:
            transaction.on_commit(lambda: create_in_app_notification(
                company=instance.company,
                title="New Bulk Transfer Request Created",
                message=f"A new bulk transfer request of ₦{instance.total_amount:,.2f} has been created: {instance.description}",
                notification_type='transfer_created',
                related_object_id=instance.id,
                related_object_type='BulkTransferRequest'
            ))
        return

    # Check if any status has changed
    status_changes = []

    if hasattr(instance, '_old_is_checked') and not instance._old_is_checked and instance.is_checked:
        status_changes.append('checked')

    if hasattr(instance, '_old_is_verified') and not instance._old_is_verified and instance.is_verified:
        status_changes.append('verified')

    if hasattr(instance, '_old_is_approved') and not instance._old_is_approved and instance.is_approved:
        status_changes.append('approved')

    if hasattr(instance, '_old_is_declined') and not instance._old_is_declined and instance.is_declined:
        status_changes.append('declined')

    # Send notifications for each status change
    for status in status_changes:
        transaction.on_commit(lambda s=status: send_bulk_transfer_status_notification(instance, s))


def send_bulk_transfer_status_notification(transfer_request, status):
    """Send notification for bulk transfer status change."""
    try:
        # For bulk transfers, notify all active customers of the company
        if not transfer_request.company:
            return

        company_customers = Customer.objects.filter(company=transfer_request.company, active=True)
        company_name = transfer_request.company.name

        # Prepare status-specific messages
        status_messages = {
            'checked': {
                'sms': f"Bulk transfer request for {company_name} (₦{transfer_request.total_amount:,.2f}) has been checked and is under review.",
                'email_subject': 'Bulk Transfer Request Checked',
                'email_body': f"""
                <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h2 style="color: #2c5aa0; margin-bottom: 20px;">Bulk Transfer Request Checked</h2>

                            <p>Dear Team,</p>

                            <p>Your bulk transfer request has been checked and is now under review.</p>

                            <div style="background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
                                <h3 style="color: #856404; margin-top: 0;">Transfer Details</h3>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold; width: 30%;">Total Amount:</td>
                                        <td style="padding: 8px 0; color: #2c5aa0; font-weight: bold; font-size: 18px;">₦{transfer_request.total_amount:,.2f}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Description:</td>
                                        <td style="padding: 8px 0;">{transfer_request.description}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Company:</td>
                                        <td style="padding: 8px 0;">{company_name}</td>
                                    </tr>
                                </table>
                            </div>

                            <p><strong>Status:</strong> The request is now in the verification stage.</p>

                            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                            <p style="color: #666; font-size: 14px;">
                                Best regards,<br>
                                <strong>Bowen MFB Team</strong>
                            </p>
                        </div>
                    </div>
                </body>
                </html>
                """
            },
            'verified': {
                'sms': f"Bulk transfer request for {company_name} (₦{transfer_request.total_amount:,.2f}) has been verified and is awaiting final approval.",
                'email_subject': 'Bulk Transfer Request Verified',
                'email_body': f"""
                <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h2 style="color: #007bff; margin-bottom: 20px;">Bulk Transfer Request Verified</h2>

                            <p>Dear Team,</p>

                            <p>Your bulk transfer request has been verified and is awaiting final approval.</p>

                            <div style="background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #007bff;">
                                <h3 style="color: #004085; margin-top: 0;">Transfer Details</h3>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold; width: 30%;">Total Amount:</td>
                                        <td style="padding: 8px 0; color: #007bff; font-weight: bold; font-size: 18px;">₦{transfer_request.total_amount:,.2f}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Description:</td>
                                        <td style="padding: 8px 0;">{transfer_request.description}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Company:</td>
                                        <td style="padding: 8px 0;">{company_name}</td>
                                    </tr>
                                </table>
                            </div>

                            <p><strong>Status:</strong> The request is now in the final approval stage.</p>

                            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                            <p style="color: #666; font-size: 14px;">
                                Best regards,<br>
                                <strong>Bowen MFB Team</strong>
                            </p>
                        </div>
                    </div>
                </body>
                </html>
                """
            },
            'approved': {
                'sms': f"Great news! Bulk transfer request for {company_name} (₦{transfer_request.total_amount:,.2f}) has been approved and will be processed.",
                'email_subject': 'Bulk Transfer Request Approved',
                'email_body': f"""
                <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h2 style="color: #28a745; margin-bottom: 20px;">✅ Bulk Transfer Request Approved</h2>

                            <p>Dear Team,</p>

                            <p><strong>Great news!</strong> Your bulk transfer request has been approved and will be processed shortly.</p>

                            <div style="background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;">
                                <h3 style="color: #155724; margin-top: 0;">Transfer Details</h3>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold; width: 30%;">Total Amount:</td>
                                        <td style="padding: 8px 0; color: #28a745; font-weight: bold; font-size: 20px;">₦{transfer_request.total_amount:,.2f}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Description:</td>
                                        <td style="padding: 8px 0;">{transfer_request.description}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Company:</td>
                                        <td style="padding: 8px 0;">{company_name}</td>
                                    </tr>
                                </table>
                            </div>

                            <p>All transfers in this batch will be executed and you will receive confirmations once completed.</p>

                            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                            <p style="color: #666; font-size: 14px;">
                                Best regards,<br>
                                <strong>Bowen MFB Team</strong>
                            </p>
                        </div>
                    </div>
                </body>
                </html>
                """
            },
            'declined': {
                'sms': f"Bulk transfer request for {company_name} (₦{transfer_request.total_amount:,.2f}) has been declined. Reason: {transfer_request.decline_reason or 'Not specified'}",
                'email_subject': 'Bulk Transfer Request Declined',
                'email_body': f"""
                <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h2 style="color: #dc3545; margin-bottom: 20px;">❌ Bulk Transfer Request Declined</h2>

                            <p>Dear Team,</p>

                            <p>We regret to inform you that your bulk transfer request has been declined.</p>

                            <div style="background-color: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #dc3545;">
                                <h3 style="color: #721c24; margin-top: 0;">Transfer Details</h3>
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold; width: 30%;">Total Amount:</td>
                                        <td style="padding: 8px 0; color: #dc3545; font-weight: bold;">₦{transfer_request.total_amount:,.2f}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Description:</td>
                                        <td style="padding: 8px 0;">{transfer_request.description}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; font-weight: bold;">Company:</td>
                                        <td style="padding: 8px 0;">{company_name}</td>
                                    </tr>
                                </table>
                            </div>

                            <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
                                <p style="margin: 0; color: #856404;">
                                    <strong>Reason for decline:</strong> {transfer_request.decline_reason or 'Not specified'}
                                </p>
                            </div>

                            <p>Please contact your account officer for more information or to submit a new request.</p>

                            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                            <p style="color: #666; font-size: 14px;">
                                Best regards,<br>
                                <strong>Bowen MFB Team</strong>
                            </p>
                        </div>
                    </div>
                </body>
                </html>
                """
            }
        }

        if status in status_messages:
            message_data = status_messages[status]

            # Send notifications to all company customers
            for customer in company_customers:
                # Send SMS notification
                send_notification_async(
                    notification_type='sms',
                    recipient=customer.phone_number,
                    message=message_data['sms']
                )

                # Send email notification
                send_notification_async(
                    notification_type='email',
                    recipient=customer.user.email,
                    message=message_data['email_body'],
                    subject=message_data['email_subject']
                )

            log_request(f"Sent {status} notification for bulk transfer {transfer_request.id} to {company_customers.count()} customers")

            # Create in-app notification
            notification_titles = {
                'checked': 'Bulk Transfer Request Checked',
                'verified': 'Bulk Transfer Request Verified',
                'approved': 'Bulk Transfer Request Approved',
                'declined': 'Bulk Transfer Request Declined'
            }

            notification_messages = {
                'checked': f"Bulk transfer request of ₦{transfer_request.total_amount:,.2f} has been checked: {transfer_request.description}",
                'verified': f"Bulk transfer request of ₦{transfer_request.total_amount:,.2f} has been verified: {transfer_request.description}",
                'approved': f"Bulk transfer request of ₦{transfer_request.total_amount:,.2f} has been approved: {transfer_request.description}",
                'declined': f"Bulk transfer request of ₦{transfer_request.total_amount:,.2f} has been declined: {transfer_request.description}"
            }

            create_in_app_notification(
                company=transfer_request.company,
                title=notification_titles.get(status, 'Bulk Transfer Status Update'),
                message=notification_messages.get(status, f'Bulk transfer status updated to {status}'),
                notification_type=f'transfer_{status}',
                related_object_id=transfer_request.id,
                related_object_type='BulkTransferRequest'
            )

    except Exception as e:
        log_request(f"Failed to send bulk transfer {status} notification: {str(e)}")


@receiver(post_save, sender=AccountSignatory)
def send_account_signatory_notifications(sender, instance, created, **kwargs):
    """Send notifications when account signatory is added or updated."""
    if created:
        # Create in-app notification for new account signatory
        if instance.company_account.company:
            transaction.on_commit(lambda: create_in_app_notification(
                company=instance.company_account.company,
                title="New Account Signatory Added",
                message=f"A new account signatory {instance.customer.user.get_full_name()} has been added for your account.",
                notification_type='account_signatory_added',
                related_object_id=instance.id,
                related_object_type='AccountSignatory'
            ))
    else:
        # Create in-app notification for updated account signatory
        if instance.company_account.company:
            transaction.on_commit(lambda: create_in_app_notification(
                company=instance.company_account.company,
                title="Account Signatory Updated",
                message=f"Account signatory {instance.customer.user.get_full_name()} has been updated for your account.",
                notification_type='account_signatory_updated',
                related_object_id=instance.id,
                related_object_type='AccountSignatory'
            ))


