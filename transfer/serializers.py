import json

from django.conf import settings
from django.utils import timezone
from rest_framework import serializers

from bowenmfb.modules.exceptions import InvalidRequestException
from bowenmfb.modules.utils import get_account_balance, log_request, check_transaction_limit
from transfer.models import *
from transfer.tasks import *


class BankListSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = BankList
        exclude = []


class TransferBeneficiarySerializerOut(serializers.ModelSerializer):
    class Meta:
        model = TransferBeneficiary
        exclude = []


class TransferSchedulerSerializerOut(serializers.ModelSerializer):
    transfer_request = serializers.SerializerMethodField()

    def get_transfer_request(self, obj):
        single_request = SingleTransferRequest.objects.filter(scheduler=obj)
        bulk_request = BulkTransferRequest.objects.filter(scheduler=obj)
        if single_request.exists():
            return SingleTransferRequestSerializerOut(single_request, many=True).data
        if bulk_request.exists():
            return BulkTransferRequestSerializerOut(bulk_request, many=True).data
        return None

    class Meta:
        model = TransferScheduler
        exclude = []


class SingleTransferRequestSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = SingleTransferRequest
        depth = 1
        exclude = []


class SingleTransferSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = SingleTransfer
        exclude = []


class BulkTransferRequestSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = BulkTransferRequest
        exclude = []


class BulkTransferSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = BulkTransfer
        exclude = []


class AccountSignatorySerializerOut(serializers.ModelSerializer):
    company_name = serializers.CharField(source='company_account.company.name', read_only=True)
    customer_name = serializers.CharField(source='customer.user.get_full_name', read_only=True)
    customer_email = serializers.CharField(source='customer.user.email', read_only=True)
    account_number = serializers.CharField(source='company_account.get_masked_account_number', read_only=True)
    role_display = serializers.CharField(source='get_role_display', read_only=True)

    class Meta:
        model = AccountSignatory
        exclude = []


class AccountSignatorySerializerIn(serializers.ModelSerializer):
    class Meta:
        model = AccountSignatory
        fields = ['customer', 'company_account', 'role', 'can_upload', 'can_check', 'can_verify', 'can_approve']


class SignatoryHierarchySerializerOut(serializers.ModelSerializer):
    signatories = AccountSignatorySerializerOut(many=True, read_only=True)

    class Meta:
        model = SignatoryHierarchy
        depth = 1
        exclude = []


class SignatoryHierarchySerializerIn(serializers.ModelSerializer):
    class Meta:
        model = SignatoryHierarchy
        fields = ['total_levels', 'requires_checker', 'requires_verifier', 'requires_approver', 'is_single_signatory']


class TransferApprovalWorkflowSerializerOut(serializers.ModelSerializer):
    uploaded_by_name = serializers.CharField(source='uploaded_by.user.get_full_name', read_only=True)
    checked_by_name = serializers.CharField(source='checked_by.user.get_full_name', read_only=True)
    verified_by_name = serializers.CharField(source='verified_by.user.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.user.get_full_name', read_only=True)
    declined_by_name = serializers.CharField(source='declined_by.user.get_full_name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)

    class Meta:
        model = TransferApprovalWorkflow
        exclude = []


class ProcessTransferApprovalSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    action = serializers.ChoiceField(choices=['check', 'verify', 'approve', 'decline'])
    remarks = serializers.CharField(required=False, allow_blank=True)
    approval_id = serializers.UUIDField()
    transaction_pin = serializers.CharField()

    def validate(self, attrs):
        user = attrs.get('user')
        transaction_pin = attrs.get('transaction_pin')
        action = attrs.get('action')
        remarks = attrs.get('remarks', '')

        customer = user.customer

        decrypted_pin = str(customer.get_decrypted_approval_pin)
        if decrypted_pin != transaction_pin:
            customer.failed_pin_retries += 1
            customer.save()
            raise InvalidRequestException({"detail": "Incorrect Transaction PIN"})

        if action == 'decline' and not remarks:
            raise InvalidRequestException({"detail": "Remarks are required when declining a transfer"})

        return attrs

    def create(self, validated_data):
        workflow_id = validated_data.get("approval_id")
        user = validated_data.get("user")
        action = validated_data.get("action")
        remarks = validated_data.get("remarks")

        try:
            workflow = TransferApprovalWorkflow.objects.get(id=workflow_id, status__in=["pending", "in_progress"])
        except TransferApprovalWorkflow.DoesNotExist:
            raise InvalidRequestException({"detail": "Transfer approval not found"})

        # Check if user can perform this action
        result, signatory = workflow.can_user_perform_action(user, action)
        if not result:
            raise InvalidRequestException({
                "detail": f"You are not authorized to {action} this transfer at the current level"
            })

        # Process the action
        current_time = timezone.now()
        customer = user.customer
        customer.failed_pin_retries = 0
        customer.save()

        if action == "check":
            if workflow.checked_by:
                raise InvalidRequestException({"detail": "Request has already been checked"})
            if workflow.declined_by:
                raise InvalidRequestException({"detail": "Request has already been declined"})

            workflow.checked_by = customer
            workflow.checked_at = current_time
            workflow.current_level = 3
            workflow.status = "in_progress"

            # Update the transfer request
            if workflow.single_transfer_request:
                workflow.single_transfer_request.is_checked = True
                workflow.single_transfer_request.checked_by = customer
                workflow.single_transfer_request.save()
            elif workflow.bulk_transfer_request:
                workflow.bulk_transfer_request.is_checked = True
                workflow.bulk_transfer_request.checked_by = customer
                workflow.bulk_transfer_request.save()

        elif action == "verify":
            if workflow.verified_by:
                raise InvalidRequestException({"detail": "Request has already been verified"})
            if workflow.declined_by:
                raise InvalidRequestException({"detail": "Request has already been declined"})

            workflow.verified_by = customer
            workflow.verified_at = current_time
            workflow.current_level = 4
            workflow.status = "in_progress"

            # Update the transfer request
            if workflow.single_transfer_request:
                workflow.single_transfer_request.is_verified = True
                workflow.single_transfer_request.verified_by = customer
                workflow.single_transfer_request.save()
            elif workflow.bulk_transfer_request:
                workflow.bulk_transfer_request.is_verified = True
                workflow.bulk_transfer_request.verified_by = customer
                workflow.bulk_transfer_request.save()
            else:
                pass

        elif action == "approve":
            if workflow.approved_by:
                raise InvalidRequestException({"detail": "Request has already been approved"})
            if workflow.declined_by:
                raise InvalidRequestException({"detail": "Request has already been declined"})

            workflow.approved_by = customer
            workflow.approved_at = current_time
            workflow.status = "approved"

            # Update the transfer request
            if workflow.single_transfer_request:
                transfer_request = workflow.single_transfer_request
                amount_to_send = transfer_request.amount
                account_number = transfer_request.from_account.account_number
                response = get_account_balance(account_number)
                balance = response.get("balances").get("withdrawable_balance")
                if amount_to_send > float(balance):
                    raise InvalidRequestException({"detail": "Insufficient balance"})
                workflow.save()
                workflow.single_transfer_request.is_approved = True
                workflow.single_transfer_request.approved_by = customer
                # Send workflow to celery
                perform_bowen_fund_transfer.delay(str(workflow.id))
                # perform_bowen_fund_transfer(str(workflow.id))
                workflow.single_transfer_request.save()
            elif workflow.bulk_transfer_request:
                workflow.bulk_transfer_request.is_approved = True
                workflow.bulk_transfer_request.approved_by = customer
                workflow.bulk_transfer_request.save()
                workflow.save()
                scheduled = "false"
                if workflow.bulk_transfer_request.scheduled:
                    scheduled = "true"
                # Send workflow to celery
                create_bulk_transfer.delay(str(workflow.bulk_transfer_request.id), scheduled)

            else:
                pass

        elif action == "decline":
            if workflow.declined_by:
                raise InvalidRequestException({"detail": "Request has already been declined"})

            if workflow.approved_by:
                raise InvalidRequestException({"detail": "Request has already been approved"})

            workflow.declined_by = customer
            workflow.declined_at = current_time
            workflow.decline_reason = remarks
            workflow.status = "declined"

            # Update the transfer request
            if workflow.single_transfer_request:
                workflow.single_transfer_request.is_declined = True
                workflow.single_transfer_request.declined_by = customer
                workflow.single_transfer_request.decline_reason = remarks
                workflow.single_transfer_request.save()
            elif workflow.bulk_transfer_request:
                workflow.bulk_transfer_request.is_declined = True
                workflow.bulk_transfer_request.declined_by = customer
                workflow.bulk_transfer_request.decline_reason = remarks
                workflow.bulk_transfer_request.save()
            else:
                pass
        else:
            pass

        workflow.save()

        return {"detail": f"Transfer {action}ed successfully", "data": TransferApprovalWorkflowSerializerOut(workflow).data}


class TransferSchedulerSerializerIn(serializers.Serializer):
    schedule_type = serializers.ChoiceField(required=False, choices=SCHEDULE_TYPE, default="once")
    day_of_the_month = serializers.ChoiceField(required=False, choices=DAYS_OF_THE_MONTH_CHOICES, default="1")
    day_of_the_week = serializers.ChoiceField(required=False, choices=DAY_OF_THE_WEEK_CHOICES, default="1")
    status = serializers.ChoiceField(required=False, choices=TRANSFER_SCHEDULE_STATUS, default="inactive")
    category = serializers.ChoiceField(required=False, choices=TRANSFER_SCHEDULE_CATEGORY_CHOICES, default="beneficiary_payment")
    start_date = serializers.DateTimeField(required=False, help_text="When to start the schedule (defaults to now)")
    end_date = serializers.DateTimeField(required=False, help_text="When to end the schedule (optional)")


class CreateSingleTransferSerializerIn(TransferSchedulerSerializerIn):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    beneficiary_type = serializers.ChoiceField(choices=TRANSFER_BENEFICIARY_TYPE_CHOICES)
    amount = serializers.FloatField(min_value=5)
    from_account_id = serializers.UUIDField()
    to_account_number = serializers.CharField()
    to_account_name = serializers.CharField()
    to_bank_name = serializers.CharField()
    to_bank_code = serializers.CharField(required=False, allow_null=False, allow_blank=False)
    narration = serializers.CharField()
    session_id = serializers.CharField(required=False, allow_null=False, allow_blank=False)
    schedule = serializers.BooleanField(default=False)

    def validate(self, attrs):
        user = attrs.get("user")
        from_account = attrs.get("from_account_id")
        amount = attrs.get("amount")
        session_id = attrs.get("session_id")
        bank_code = attrs.get("to_bank_code")
        transfer_type = attrs.get("transfer_type")
        # Validate user is member of from_account
        try:
            from_acct = CompanyAccount.objects.get(id=from_account, company=user.customer.company)
        except CompanyAccount.DoesNotExist:
            raise InvalidRequestException({"detail": "Selected account not valid"})

        # Check if logged in user is a signatory of the from_account and can upload transfer requests
        try:
            signatory = AccountSignatory.objects.get(company_account=from_acct, customer=user.customer, is_active=True)
            if not signatory.can_upload:
                raise InvalidRequestException({"detail": "You are not authorized to perform this action"})
        except AccountSignatory.DoesNotExist:
            raise InvalidRequestException({"detail": "You are not authorized to perform this action"})

        # Check account limit
        check_transaction_limit(from_account, amount)

        # Validate from_account balance is sufficient to perform transfer
        account_number = from_acct.account_number
        response = get_account_balance(account_number)
        balance = response.get("balances").get("withdrawable_balance")

        if amount > float(balance):
            raise InvalidRequestException({"detail": "Insufficient balance"})

        # Confirm session_id for inter-bank transfer
        if transfer_type == "inter":
            if not session_id:
                raise InvalidRequestException({"detail": "SessionID is required for Inter-Bank Transaction"})
            if not bank_code:
                raise InvalidRequestException({"detail": "Receiving Bank is required for Inter-Bank Transaction"})

        return attrs

    def create(self, validated_data):
        user = validated_data.get("user")
        transfer_type = validated_data.get("beneficiary_type")
        amount = validated_data.get("amount")
        from_account_id = validated_data.get("from_account_id")
        to_account_number = validated_data.get("to_account_number")
        to_account_name = str(validated_data.get("to_account_name")).upper()
        to_bank_name = str(validated_data.get("to_bank_name")).upper()
        to_bank_code = validated_data.get("to_bank_code")
        narration = str(validated_data.get("narration")).upper()
        session_id = validated_data.get("session_id", "")
        schedule = validated_data.get("schedule", False)

        bank_name = "BOWEN MFB"
        if transfer_type == "inter":
            bank_name = to_bank_name

        customer = user.customer

        transfer_request = SingleTransferRequest.objects.create(
            company=customer.company, amount=amount, description=narration, from_account_id=from_account_id,
            beneficiary_account_number=to_account_number, beneficiary_name=to_account_name, beneficiary_bank_code=to_bank_code,
            nip_session_id=session_id, bank_name=bank_name, scheduled=schedule, beneficiary_type=transfer_type, created_by=customer
        )
        if schedule:
            schedule_type = validated_data.get("schedule_type")
            dom = validated_data.get("day_of_the_month")
            dow = validated_data.get("day_of_the_week")
            scheduler_status = validated_data.get("status")
            category = validated_data.get("category")
            start_date = validated_data.get("start_date")
            end_date = validated_data.get("end_date")

            if not all([category, schedule_type, dom, dow]):
                raise InvalidRequestException({"detail": "Select a schedule type, category, day of the month and day of the week"})

            scheduler_data = {
                'company': customer.company,
                'schedule_type': schedule_type,
                'day_of_the_month': dom,
                'day_of_the_week': dow,
                'status': scheduler_status,
                'category': category
            }

            if start_date:
                scheduler_data['start_date'] = start_date
            if end_date:
                scheduler_data['end_date'] = end_date

            scheduler = TransferScheduler.objects.create(**scheduler_data)
            transfer_request.scheduler = scheduler
            transfer_request.scheduled = True
            transfer_request.save()

        # Create TransferApprovalWorkflow
        TransferApprovalWorkflow.objects.create(single_transfer_request=transfer_request, uploaded_by=customer, uploaded_at=transfer_request.created_at)

        return SingleTransferRequestSerializerOut(transfer_request).data


class AddTransferBeneficiarySerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    name = serializers.CharField()
    bank_name = serializers.CharField()
    bank_code = serializers.CharField(required=False)
    account_number = serializers.CharField()
    beneficiary_type = serializers.ChoiceField(choices=TRANSFER_BENEFICIARY_TYPE_CHOICES)

    def validate(self, attrs):
        account_number = attrs.get("account_number")
        if not (len(account_number) == 10 and str(account_number).isnumeric()):
            raise InvalidRequestException({"detail": "Please provide a valid account number"})
        return attrs

    def create(self, validated_data):
        user = validated_data.get("user")
        name = validated_data.get("name")
        bank_name = validated_data.get("bank_name")
        bank_code = validated_data.get("bank_code")
        account_number = validated_data.get("account_number")
        beneficiary_type = validated_data.get("beneficiary_type")

        company = user.customer.company

        try:
            TransferBeneficiary.objects.get(company=company, account_number=account_number)
            raise InvalidRequestException({"detail": "Beneficiary already exist"})
        except TransferBeneficiary.DoesNotExist:
            pass

        beneficiary = TransferBeneficiary.objects.create(
            company=company, name=name, bank_name=bank_name, bank_code=bank_code, account_number=account_number,
            beneficiary_type=beneficiary_type
        )

        return {"detail": "Beneficiary added successfully", "data": TransferBeneficiarySerializerOut(beneficiary).data}


class DownloadStatementSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    account_id = serializers.UUIDField()
    from_date = serializers.DateField(help_text="YYYY-MM-DD")
    to_date = serializers.DateField(help_text="YYYY-MM-DD")

    def validate(self, attrs):
        from_date = attrs.get("from_date")
        to_date = attrs.get("to_date")
        account_id = attrs.get("account_id")
        user = attrs.get("user")

        if not CompanyAccount.objects.filter(id=account_id, company=user.customer.company).exists():
            raise InvalidRequestException({"detail": "Account not found"})

        if from_date > to_date:
            raise InvalidRequestException({"detail": "From date cannot be greater than to date"})

        return attrs

    def create(self, validated_data):
        account_id = validated_data.get("account_id")
        from_date = validated_data.get("from_date")
        to_date = validated_data.get("to_date")

        account_number = CompanyAccount.objects.get(id=account_id).account_number

        try:
            response = client.generate_account_statement(account_number=account_number, from_date=from_date, to_date=to_date)
            if "IsSuccessful" in response and response.get("IsSuccessful") is True and response.get("Message"):
                return {"detail": "Statement generated successfully", "data": response.get("Message")}
            else:
                raise InvalidRequestException({"detail": "Failed to generate statement, pleas e try again later"})
        except Exception as err:
            log_request(f"STATEMENT GENERATION ERROR\n{err}")
            raise InvalidRequestException({"detail": "Error occurred while generating statement, please contact support"})



class BulkTransferRequestSerializerIn(TransferSchedulerSerializerIn):
    current_user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    data = serializers.ListField(child=serializers.DictField())
    schedule = serializers.BooleanField(required=False)
    description = serializers.CharField(max_length=190)

    def create(self, validated_data):
        user = validated_data.get('current_user')
        data = validated_data.get("data")
        description = validated_data.get("description")
        schedule = validated_data.get("schedule", False)

        payload = json.dumps(data)

        amount_to_send = 0.00
        for item in data:
            if "amount" in item and item["amount"]:
                amount_to_send += float(item["amount"])

        company = user.customer.company

        # Check account limit
        account_id = company.companyaccount_set.last().id
        check_transaction_limit(account_id, amount_to_send)

        # Create Bulk Transfer Request
        bulk_trans = BulkTransferRequest.objects.create(
            company=company, description=description, request_payload=payload, total_amount=amount_to_send
        )

        if schedule:
            schedule_type = validated_data.get("schedule_type")
            dom = validated_data.get("day_of_the_month")
            dow = validated_data.get("day_of_the_week")
            scheduler_status = validated_data.get("status")
            category = validated_data.get("category")
            start_date = validated_data.get("start_date")
            end_date = validated_data.get("end_date")

            if not all([category, schedule_type, dom, dow]):
                raise InvalidRequestException({"detail": "Select a schedule type, category, day of the month and day of the week"})

            scheduler_data = {
                'company': company,
                'schedule_type': schedule_type,
                'day_of_the_month': dom,
                'day_of_the_week': dow,
                'status': scheduler_status,
                'category': category
            }

            if start_date:
                scheduler_data['start_date'] = start_date
            if end_date:
                scheduler_data['end_date'] = end_date

            scheduler = TransferScheduler.objects.create(**scheduler_data)
            bulk_trans.scheduler = scheduler
            bulk_trans.scheduled = True
            bulk_trans.save()

        # Create TransferApprovalWorkflow
        TransferApprovalWorkflow.objects.create(bulk_transfer_request=bulk_trans, uploaded_by=user.customer, uploaded_at=bulk_trans.created_at)

        return {"detail": "Bulk transfer request submitted successfully", "data": BulkTransferRequestSerializerOut(bulk_trans).data}


class EditTransferSchedulerSerializerIn(TransferSchedulerSerializerIn):
    def update(self, instance, validated_data):
        # Check if schedule-related fields are being updated
        schedule_fields_changed = any([
            validated_data.get("schedule_type") != instance.schedule_type,
            validated_data.get("day_of_the_month") != instance.day_of_the_month,
            validated_data.get("day_of_the_week") != instance.day_of_the_week,
            validated_data.get("start_date") != instance.start_date,
        ])

        instance.schedule_type = validated_data.get("schedule_type", instance.schedule_type)
        instance.day_of_the_month = validated_data.get("day_of_the_month", instance.day_of_the_month)
        instance.day_of_the_week = validated_data.get("day_of_the_week", instance.day_of_the_week)
        instance.status = validated_data.get("status", instance.status)
        instance.category = validated_data.get("category", instance.category)

        # Update start_date and end_date if provided
        if "start_date" in validated_data:
            instance.start_date = validated_data["start_date"]
        if "end_date" in validated_data:
            instance.end_date = validated_data["end_date"]

        # Recalculate next_job_date if schedule fields changed
        if schedule_fields_changed:
            instance.next_job_date = instance.calculate_next_job_date()

        instance.save()
        return {"detail": "Scheduler updated successfully", "data": TransferSchedulerSerializerOut(instance).data}

