import decimal
import os

from django.db.models import Q
from django.http import Http404, HttpResponse
from drf_spectacular.utils import extend_schema, OpenApiParameter
from rest_framework import generics, status, filters
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response

from bowenmfb.modules.exceptions import raise_serializer_error_msg
from bowenmfb.modules.permissions import TooManyPINRetriesException, HasCompanyAccount
from bowenmfb.modules.paginations import CustomPagination
from .serializers import *

client = BankOneClient()


class ListBankGenericAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = BankListSerializerOut
    queryset = BankList.objects.all().order_by("name")
    # pagination_class = CustomPagination


class NameEnquiryAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(
        parameters=[OpenApiParameter(name="bank_id", type=str, required=False), OpenApiParameter(name="account_number", type=str, required=True),
                    OpenApiParameter(name="beneficiary_type", type=str, required=True)],
        responses={status.HTTP_200_OK}
    )
    def get(self, request):
        bank_id = request.GET.get("bank_id")
        account_no = request.GET.get("account_number")
        enquiry_type = request.GET.get("beneficiary_type", "intra")  # intra or inter

        session_id = ""
        account_name = ""
        bank_name = "Bowen MFB"

        if not (len(account_no) == 10 and str(account_no).isnumeric()):
            raise InvalidRequestException({"detail": "Please provide a valid account number"})

        if enquiry_type == "intra":
            response = client.get_customer_by_account_number(account_no)
            print(response)
            if "Name" in response and response["Name"] is not None or "":
                account_name = response["Name"]
            elif "BusinessName" in response and response["BusinessName"] is not None or "":
                account_name = response["BusinessName"]
            elif ("LastName" and "OtherNames" in response) and (response["LastName"] is not None or response["OtherNames"] is not None):
                account_name = str(response["LastName"]) + " " + str(response["OtherNames"])
            else:
                raise InvalidRequestException({"detail": "Account not found"})

        elif enquiry_type == "inter":
            if not bank_id:
                raise InvalidRequestException({"detail": "Please select a bank to continue"})
            try:
                bank = BankList.objects.get(id=bank_id)
            except BankList.DoesNotExist:
                raise InvalidRequestException({"detail": "Invalid bank selection"})
            bank_list_code = bank.code
            bank_name = bank.name
            response = client.name_enquiry(account_number=account_no, bank_code=bank_list_code)
            if "IsSuccessful" in response and response["IsSuccessful"] is True:
                account_name = response["Name"]
                session_id = response["SessionID"]
                if settings.DEBUG is True:
                    session_id = "23456789876543234567898765432345"
            else:
                raise InvalidRequestException({"detail": "Account not found"})
        else:
            raise InvalidRequestException({"detail": "Invalid beneficiary type. Choices: 'intra' or 'inter'"})

        return Response({
            "detail": "Account information retrieved", "data": {"bank_name": bank_name, "account_name": account_name, "session_id": session_id}
        })


class AccountBalanceAPIView(APIView):
    permission_classes = [HasCompanyAccount]

    def get(self, request):
        company = request.user.customer.company
        if not company:
            raise InvalidRequestException({"detail": "You need to be part of a company to view balances"})

        result = list()
        for account in CompanyAccount.objects.filter(company=company):
            account_number = account.account_number
            balance_data = get_account_balance(account_number)
            result.append(balance_data)
        return Response({"detail": "Account balances retrieved", "data": result})


class TransactionHistoryAPIView(APIView):
    permission_classes = [HasCompanyAccount]

    @extend_schema(
        parameters=[OpenApiParameter(name="date_from", type=str), OpenApiParameter(name="account_id", type=str, required=True),
                    OpenApiParameter(name="date_to", type=str), OpenApiParameter(name="page_no", type=str)],
        responses={status.HTTP_200_OK}
    )
    def get(self, request):
        account_id = request.GET.get("account_id")
        date_from = request.GET.get("date_from")
        date_to = request.GET.get("date_to")
        page_no = request.GET.get("page_no")

        company = request.user.customer.company

        account = CompanyAccount.objects.filter(id=account_id, company=company)

        if not account.exists():
            raise InvalidRequestException({"detail": "Selected account not valid"})

        account_no = account.last().account_number

        response = client.paginated_transaction_history(
            account_number=account_no, page_number=page_no, transaction_date_from=date_from, transaction_date_to=date_to
        )
        result = list()
        pages = dict()

        if "IsSuccessful" in response and response["IsSuccessful"] is True:
            messages = response["Message"]["data"]
            paging = response["Message"]["page"]
            pages["size"] = paging["size"]
            pages["item_count"] = paging["totalCount"]
            pages["page_count"] = paging["totalPages"]
            for item in messages:
                message = dict()
                message["date"] = item["TransactionDate"]
                message["date_string"] = item["TransactionDateString"]
                message["direction"] = item["RecordType"]
                message["amount"] = decimal.Decimal(item["Amount"]) / 100
                message["description"] = item["Narration"]
                message["reference_no"] = item["InstrumentNo"]
                result.append(message)

        return Response({"detail": "Transaction history retrieved successfully", "result": result, "pagination": pages})


class TransferApprovalWorkflowAPIView(APIView):
    """
    View and manage transfer approval workflows.
    """
    permission_classes = [IsAuthenticated]

    @extend_schema(
        parameters=[
            OpenApiParameter(name="transfer_type", type=str, required=True, description="'single' or 'bulk'"),
            OpenApiParameter(name="transfer_id", type=str, required=True, description="uuid of the single or bulk transfer request")

        ],
        responses={status.HTTP_200_OK: TransferApprovalWorkflowSerializerOut}
    )
    def get(self, request):
        transfer_type = request.GET.get("transfer_type")
        transfer_id = request.GET.get("transfer_id")
        company = request.user.customer.company

        try:
            if transfer_type == "single":
                transfer = SingleTransferRequest.objects.get(id=transfer_id, company=company)
                workflow = getattr(transfer, 'approval_workflow', None)
            elif transfer_type == "bulk":
                transfer = BulkTransferRequest.objects.get(id=transfer_id, company=company)
                workflow = getattr(transfer, 'approval_workflow', None)
            else:
                raise InvalidRequestException({"detail": "Invalid transfer type. Use 'single' or 'bulk'"})

            if not workflow:
                raise InvalidRequestException({"detail": "Workflow not found for this transfer"})

            serializer = TransferApprovalWorkflowSerializerOut(workflow)
            return Response({
                "detail": "Transfer approval retrieved successfully",
                "data": serializer.data
            })
        except (SingleTransferRequest.DoesNotExist, BulkTransferRequest.DoesNotExist):
            raise InvalidRequestException({"detail": "Transfer not found"})


class ProcessTransferApprovalAPIView(APIView):
    """
    Process transfer approval actions (check, verify, approve, decline).
    """
    permission_classes = [TooManyPINRetriesException]

    @extend_schema(request=ProcessTransferApprovalSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = ProcessTransferApprovalSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class BulkTransferTemplateDownloadAPIView(APIView):
    """
    Download the CSV template for bulk transfers.
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        template_path = os.path.join(settings.BASE_DIR, 'static', 'templates', 'bulk_transfer_template.csv')

        if not os.path.exists(template_path):
            raise Http404("Template file not found")

        with open(template_path, 'rb') as template_file:
            response = HttpResponse(template_file.read(), content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename="bulk_transfer_template.csv"'
            return response


class CreateSingleTransferRequestAPIView(APIView):
    permission_classes = [HasCompanyAccount]

    @extend_schema(request=CreateSingleTransferSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = CreateSingleTransferSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response({"detail": "Transfer request submitted successfully", "data": response})


@extend_schema(
    parameters=[
        OpenApiParameter(name="search", type=str, description="Search by description, beneficiary name and account number"),
        OpenApiParameter(name="date_from", type=str, description="filter date created: YYYY-MM-DD"),
        OpenApiParameter(name="date_to", type=str, description="filter date created: YYYY-MM-DD"),
        OpenApiParameter(name="beneficiary_type", type=str, description="filter by beneficiary type"),
        OpenApiParameter(name="amount_from", type=str, description="filter by amount from"),
        OpenApiParameter(name="amount_to", type=str, description="filter by amount to"),
    ],
    responses={status.HTTP_200_OK: SingleTransferRequestSerializerOut}
)
class ListSingleTransferRequest(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = SingleTransferRequestSerializerOut
    pagination_class = CustomPagination
    lookup_field = "id"

    def get_queryset(self):
        query = Q(company_id=self.request.user.customer.company_id)
        search = self.request.GET.get("search", None)
        date_from = self.request.GET.get("date_from", None)
        date_to = self.request.GET.get("date_to", None)
        beneficiary_type = self.request.GET.get("beneficiary_type", None)
        amount_from = self.request.GET.get("amount_from", None)
        amount_to = self.request.GET.get("amount_to", None)

        if search:
            query &= Q(description__icontains=search) | Q(beneficiary_name__icontains=search) | Q(beneficiary_account_number__icontains=search)
        if date_from and date_to:
            query &= Q(created_at__gte=date_from, created_at__lte=date_to)
        if beneficiary_type:
            query &= Q(beneficiary_type=beneficiary_type)
        if amount_from and amount_to:
            query &= Q(amount__gte=amount_from, amount__lte=amount_to)
        return SingleTransferRequest.objects.filter(query).order_by("-created_at")


@extend_schema(
    parameters=[
        OpenApiParameter(name="search", type=str, description="Search by description, beneficiary name and account number"),
        OpenApiParameter(name="status", type=str, description="Search by status"),
        OpenApiParameter(name="date_from", type=str, description="filter date created: YYYY-MM-DD"),
        OpenApiParameter(name="date_to", type=str, description="filter date created: YYYY-MM-DD"),
        OpenApiParameter(name="amount_from", type=str, description="filter by amount from"),
        OpenApiParameter(name="amount_to", type=str, description="filter by amount to"),
    ],
    responses={status.HTTP_200_OK: SingleTransferSerializerOut}
)
class SingleTransferListAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = SingleTransferSerializerOut
    pagination_class = CustomPagination
    lookup_field = "id"

    def get_queryset(self):
        query = Q(company_id=self.request.user.customer.company_id)
        search = self.request.GET.get("search", None)
        query_status = self.request.GET.get("status", None)
        date_from = self.request.GET.get("date_from", None)
        date_to = self.request.GET.get("date_to", None)
        amount_from = self.request.GET.get("amount_from", None)
        amount_to = self.request.GET.get("amount_to", None)

        if search:
            query &= Q(narration__icontains=search) | Q(beneficiary_name__icontains=search) | Q(beneficiary_acct_number__icontains=search)
        if query_status:
            query &= Q(status=query_status)
        if date_from and date_to:
            query &= Q(created_at__gte=date_from, created_at__lte=date_to)
        if amount_from and amount_to:
            query &= Q(amount__gte=amount_from, amount__lte=amount_to)
        return SingleTransfer.objects.filter(query).order_by("-created_at")


@extend_schema(
    parameters=[
        OpenApiParameter(name="search", type=str, description="Search by narration, beneficiary name and account number"),
        OpenApiParameter(name="status", type=str, description="Search by status"),
        OpenApiParameter(name="date_from", type=str, description="filter date created: YYYY-MM-DD"),
        OpenApiParameter(name="date_to", type=str, description="filter date created: YYYY-MM-DD"),
        OpenApiParameter(name="amount_from", type=str, description="filter by amount from"),
        OpenApiParameter(name="amount_to", type=str, description="filter by amount to"),
    ],
    responses={status.HTTP_200_OK: BulkTransferSerializerOut}
)
class BulkTransferListAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = BulkTransferSerializerOut
    pagination_class = CustomPagination
    lookup_field = "id"

    def get_queryset(self):
        query = Q(company_id=self.request.user.customer.company_id)
        search = self.request.GET.get("search", None)
        query_status = self.request.GET.get("status", None)
        date_from = self.request.GET.get("date_from", None)
        date_to = self.request.GET.get("date_to", None)
        amount_from = self.request.GET.get("amount_from", None)
        amount_to = self.request.GET.get("amount_to", None)

        if search:
            query &= Q(narration__icontains=search) | Q(beneficiary_name__icontains=search) | Q(beneficiary_acct_number__icontains=search)
        if query_status:
            query &= Q(status=query_status)
        if date_from and date_to:
            query &= Q(created_at__gte=date_from, created_at__lte=date_to)
        if amount_from and amount_to:
            query &= Q(amount__gte=amount_from, amount__lte=amount_to)
        return BulkTransfer.objects.filter(query).order_by("-created_at")


class AddTransferBeneficiaryAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=AddTransferBeneficiarySerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = AddTransferBeneficiarySerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


@extend_schema(
    parameters=[
        OpenApiParameter(name="beneficiary_type", type=str, description="'intra' or 'inter' default is intra"),
        OpenApiParameter(name="search", type=str, description="Search by name, bank name and account number")

    ],
    responses={status.HTTP_200_OK: TransferBeneficiarySerializerOut}
)
class TransferBeneficiaryListAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = TransferBeneficiarySerializerOut
    pagination_class = CustomPagination
    lookup_field = "id"
    filter_backends = [filters.SearchFilter]
    search_fields = ["name", "bank_name", "account_number"]

    def get_queryset(self):
        benefactor_type = self.request.GET.get("beneficiary_type", "intra")
        return TransferBeneficiary.objects.filter(company_id=self.request.user.customer.company_id, beneficiary_type=benefactor_type).order_by("name")


class DeleteTransferBeneficiary(generics.DestroyAPIView):
    permission_classes = [IsAuthenticated]
    lookup_field = "id"
    serializer_class = TransferBeneficiarySerializerOut
    def get_queryset(self):
        return TransferBeneficiary.objects.get(company_id=self.request.user.customer.company_id, id=self.kwargs.get("id"))

    def delete(self, request, *args, **kwargs):
        instance = self.get_queryset()
        instance.delete()
        return Response({"detail": "Beneficiary deleted successfully"})


class StatementDownloadAPIView(APIView):
    permission_classes = [HasCompanyAccount]

    @extend_schema(request=DownloadStatementSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = DownloadStatementSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class BulkTransferRequestAPIView(APIView):
    permission_classes = [HasCompanyAccount]

    @extend_schema(request=BulkTransferRequestSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = BulkTransferRequestSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


@extend_schema(
    parameters=[
        OpenApiParameter(name="search", type=str, description="Search by description"),
        OpenApiParameter(name="date_from", type=str, description="filter date created: YYYY-MM-DD"),
        OpenApiParameter(name="date_to", type=str, description="filter date created: YYYY-MM-DD"),
        OpenApiParameter(name="amount_from", type=str, description="filter by amount from"),
        OpenApiParameter(name="amount_to", type=str, description="filter by amount to"),
    ],
    responses={status.HTTP_200_OK: BulkTransferRequestSerializerOut}
)
class BulkTransferRequestListAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = BulkTransferRequestSerializerOut
    pagination_class = CustomPagination
    lookup_field = "id"

    def get_queryset(self):
        query = Q(company_id=self.request.user.customer.company_id)
        search = self.request.GET.get("search", None)
        date_from = self.request.GET.get("date_from", None)
        date_to = self.request.GET.get("date_to", None)
        amount_from = self.request.GET.get("amount_from", None)
        amount_to = self.request.GET.get("amount_to", None)

        if search:
            query &= Q(description__icontains=search)
        if date_from and date_to:
            query &= Q(created_at__gte=date_from, created_at__lte=date_to)
        if amount_from and amount_to:
            query &= Q(total_amount__gte=amount_from, total_amount__lte=amount_to)
        return BulkTransferRequest.objects.filter(query).order_by("-created_at")


@extend_schema(
    parameters=[
        OpenApiParameter(name="category", type=str, description="'beneficiary_payment' or 'employee_payment' or 'vendor_payment'"),
        OpenApiParameter(name="date_from", type=str, description="filter date created: YYYY-MM-DD"),
        OpenApiParameter(name="date_to", type=str, description="filter date created: YYYY-MM-DD")
    ],
    responses={status.HTTP_200_OK: TransferSchedulerSerializerOut}
)
class ScheduledTransferListAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = TransferSchedulerSerializerOut
    pagination_class = CustomPagination
    lookup_field = "id"

    def get_queryset(self):
        category = self.request.GET.get("category")
        date_from = self.request.GET.get("date_from", None)
        date_to = self.request.GET.get("date_to", None)

        query = Q(company_id=self.request.user.customer.company_id)

        if category:
            query &= Q(category=category)

        if date_from and date_to:
            query &= Q(next_job_date__gte=date_from, next_job_date__lte=date_to)

        return TransferScheduler.objects.filter(query).order_by("-created_at")


class EditTransferSchedulerAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=EditTransferSchedulerSerializerIn, responses={status.HTTP_200_OK})
    def put(self, request, scheduler_id):
        try:
            instance = TransferScheduler.objects.get(id=scheduler_id, company_id=request.user.customer.company_id)
        except TransferScheduler.DoesNotExist:
            raise InvalidRequestException({"detail": "Transfer scheduler not found"})

        serializer = EditTransferSchedulerSerializerIn(data=request.data, context={"request": request}, instance=instance)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class ListCompanyAccountSignatoriesAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = AccountSignatorySerializerOut
    pagination_class = CustomPagination
    lookup_field = "id"

    def get_queryset(self):
        return AccountSignatory.objects.filter(company_account__company_id=self.request.user.customer.company_id).order_by("-created_at")

