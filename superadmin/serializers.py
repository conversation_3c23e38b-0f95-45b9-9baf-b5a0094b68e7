import json

from celery.worker.state import total_count
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from django.contrib.auth.password_validation import validate_password
from django.contrib.auth.hashers import check_password, make_password
from django.utils import timezone
from django.db.models import Q, Sum, Count
from rest_framework import serializers
from rest_framework_simplejwt.tokens import RefreshToken

from account.models import Company, Customer, CompanyAccount
from account.serializers import CompanyCreationRequestSerializerOut, CompanyAccountSerializerOut
from bowenmfb.modules.choices import ADMIN_PERMISSION_CHOICES
from bowenmfb.modules.utils import send_notification_async, log_admin_action
from transfer.models import SingleTransferRequest, BulkTransferRequest
from billpayment.models import BillPaymentRequest, BillPayment
from bowenmfb.modules.exceptions import InvalidRequestException
from .models import AdminUser, AdminRole, AdminPermission, AccountCreationRequest, AdminAuditTrail
from bowenmfb.modules.bankone import BankOneClient
from .tasks import create_corporate_account_with_bowen

client = BankOneClient()


class AdminLoginSerializerIn(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField()

    def create(self, validated_data):
        email = validated_data.get("email")
        password = validated_data.get("password")

        user = authenticate(username=email, password=password)
        if not user:
            raise InvalidRequestException({"detail": "Invalid email or password"})

        try:
            admin_user = AdminUser.objects.get(user=user, is_active=True)
        except AdminUser.DoesNotExist:
            raise InvalidRequestException({"detail": "User is not an admin or account is inactive"})

        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token

        # Log admin login action
        request = self.context.get('request')
        if request:
            log_admin_action(
                admin_user=admin_user,
                action='admin_login',
                description=f"Admin user logged in: {user.get_full_name()}",
                target_model='AdminUser',
                target_id=str(admin_user.id),
                request=request,
                additional_data={
                    'role': admin_user.role.name if admin_user.role else None,
                    'permissions_count': admin_user.permissions.count()
                }
            )

        return {
            "detail": "Login successful",
            "access_token": str(access_token),
            "refresh_token": str(refresh),
            "user": {
                "id": str(admin_user.id),
                "email": user.email,
                "full_name": user.get_full_name(),
                "role": admin_user.role.name if admin_user.role else None,
                "permissions": list(admin_user.permissions.values_list('name', flat=True))
            }
        }


class CompanySerializerOut(serializers.ModelSerializer):
    accounts = serializers.SerializerMethodField()
    signatories = serializers.SerializerMethodField()

    class Meta:
        model = Company
        exclude = ["corporate_customer_id"]
        # fields = ['id', 'name', 'email', 'phone_number', 'address', 'website', 'active', 'created_at', 'total_customers', 'active_customers']

    def get_signatories(self, obj):
        customers = Customer.objects.filter(company=obj)
        total_count = customers.count()
        active_count = customers.filter(active=True).count()
        return {
            "total": total_count,
            "active": active_count,
            "data": CustomerSerializerOut(customers, many=True).data
        }

    def get_accounts(self, obj):
        return CompanyAccountSerializerOut(CompanyAccount.objects.filter(company=obj), many=True).data


class CustomerSerializerOut(serializers.ModelSerializer):
    email = serializers.CharField(source='user.email')
    full_name = serializers.CharField(source='user.get_full_name')
    username = serializers.CharField(source='user.username')
    date_joined = serializers.CharField(source='user.date_joined')
    last_login = serializers.CharField(source='user.last_login')
    company_name = serializers.CharField(source='company.name')
    bvn_number = serializers.CharField(source='get_decrypted_bvn')
    nin_number = serializers.CharField(source='get_decrypted_nin')

    class Meta:
        model = Customer
        exclude = ["approval_pin", "verification_token", "verification_token_expiry"]


class SetCustomerInactiveSerializerIn(serializers.Serializer):
    customer_id = serializers.UUIDField()
    reason = serializers.CharField(max_length=500, required=False)

    def create(self, validated_data):
        customer_id = validated_data.get("customer_id")
        reason = validated_data.get("reason", "")

        try:
            customer = Customer.objects.get(id=customer_id)
        except Customer.DoesNotExist:
            raise InvalidRequestException({"detail": "Customer not found"})

        customer.active = False
        customer.save()

        # Log admin action
        admin_user = self.context.get('admin_user')
        request = self.context.get('request')
        if admin_user and request:
            log_admin_action(
                admin_user=admin_user,
                action='set_customer_inactive',
                description=f"Set customer as inactive: {customer.user.get_full_name()}",
                target_model='Customer',
                target_id=str(customer_id),
                request=request,
                additional_data={
                    'customer_name': customer.user.get_full_name(),
                    'customer_email': customer.user.email,
                    'company_name': customer.company.name,
                    'reason': reason
                }
            )

        return {"detail": f"Customer {customer.user.get_full_name()} has been set as inactive"}


class AccountCreationRequestSerializerOut(serializers.ModelSerializer):
    company_data = serializers.SerializerMethodField()
    checked_by_name = serializers.CharField(source='checked_by.user.get_full_name', allow_null=True)
    verified_by_name = serializers.CharField(source='verified_by.user.get_full_name', allow_null=True)
    approved_by_name = serializers.CharField(source='approved_by.user.get_full_name', allow_null=True)

    def get_company_data(self, obj):
        return CompanyCreationRequestSerializerOut(obj.creation_request).data

    class Meta:
        model = AccountCreationRequest
        exclude = []


class ProcessAccountRequestSerializerIn(serializers.Serializer):
    request_id = serializers.UUIDField()
    action = serializers.ChoiceField(choices=['check', 'verify', 'approve', 'decline'])
    remarks = serializers.CharField(max_length=500, required=False)
    
    def validate(self, attrs):
        action = attrs.get("action")
        if action == 'decline' and not attrs.get('remarks'):
            raise InvalidRequestException({"detail": "Remarks are required when declining a request"})
        return attrs

    def create(self, validated_data):
        request_id = validated_data.get("request_id")
        action = validated_data.get("action")
        remarks = validated_data.get("remarks", "")

        # Get admin user from context
        admin_user = self.context['admin_user']

        try:
            account_request = AccountCreationRequest.objects.get(id=request_id)
        except AccountCreationRequest.DoesNotExist:
            raise InvalidRequestException({"detail": "Account creation request not found"})

        current_time = timezone.now()

        if action == 'check':
            if account_request.checked_by or account_request.approved_by:
                raise InvalidRequestException({"detail": "Request has passed the check stage"})
            account_request.checked_by = admin_user
            account_request.checked_at = current_time
        elif action == 'verify':
            if account_request.approved_by:
                raise InvalidRequestException({"detail": "Request has passed the verification stage"})
            if not account_request.checked_by:
                raise InvalidRequestException({"detail": "Request must be checked before verification"})
            account_request.verified_by = admin_user
            account_request.verified_at = current_time
        elif action == 'approve':
            if account_request.rejected_by:
                raise InvalidRequestException({"detail": "Request was initially rejected"})
            if not account_request.verified_by:
                raise InvalidRequestException({"detail": "Request must be verified before approval"})
            account_request.approved_by = admin_user
            account_request.approved_at = current_time
            account_request.status = 'success'
            create_corporate_account_with_bowen.delay(str(account_request.id), str(admin_user.id))
        elif action == 'decline':
            if account_request.approved_by:
                raise InvalidRequestException({"detail": "Cannot change status for approved requests"})
            if not remarks:
                raise InvalidRequestException({"detail": "Please add a reason for declining this request"})
            account_request.status = 'failed'
            account_request.rejected_by = admin_user
            account_request.rejected_at = current_time
            
            # Send email and sms to the company stating the reason for declining
            email_message = f"""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                        <h2 style="color: #dc3545; margin-bottom: 20px;">❌ Account Creation Request Declined</h2>

                        <p>Dear <strong>{account_request.creation_request.business_name}</strong>,</p>

                        <p>We regret to inform you that your account creation request has been declined.</p>

                        <div style="background-color: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #dc3545;">
                            <h3 style="color: #721c24; margin-top: 0;">Reason for Decline</h3>
                            <p style="margin: 0; font-weight: bold;">{remarks}</p>
                        </div>

                        <div style="background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #007bff;">
                            <p style="margin: 0; color: #004085;">
                                <strong>Next Steps:</strong> Please contact your account officer for more information or to submit a new request with the necessary corrections.
                            </p>
                        </div>

                        <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                        <p style="color: #666; font-size: 14px;">
                            Best regards,<br>
                            <strong>Bowen MFB Team</strong>
                        </p>
                    </div>
                </div>
            </body>
            </html>
            """

            send_notification_async(
                notification_type='email',
                recipient=account_request.creation_request.business_email,
                message=email_message,
                subject='Account Creation Request Declined'
            )
            send_notification_async(
                notification_type='sms',
                recipient=account_request.creation_request.business_phone_number,
                message=f"Your account creation request has been declined. Reason: {remarks}"
            )

        account_request.remarks = remarks
        account_request.save()

        return {"detail": f"Account request has been {action}ed successfully"}


class SingleTransferSerializerOut(serializers.ModelSerializer):
    """Serializer for single transfer output"""
    company_name = serializers.CharField(source='company.name', allow_null=True)

    class Meta:
        model = SingleTransferRequest
        fields = ['id', 'company_name', 'amount', 'description', 'beneficiary_name',
                  'beneficiary_account_number', 'bank_name', 'is_checked', 'is_verified',
                  'is_approved', 'is_declined', 'created_at']


class BulkTransferSerializerOut(serializers.ModelSerializer):
    """Serializer for bulk transfer output"""
    company_name = serializers.CharField(source='company.name', allow_null=True)

    class Meta:
        model = BulkTransferRequest
        fields = ['id', 'company_name', 'total_amount', 'description',
                  'is_checked', 'is_verified', 'is_approved', 'is_declined', 'created_at']


class AdminUserSerializerOut(serializers.ModelSerializer):
    """Serializer for admin user output"""
    email = serializers.CharField(source='user.email')
    full_name = serializers.CharField(source='user.get_full_name')
    role_name = serializers.CharField(source='role.name', allow_null=True)
    permissions = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', allow_null=True)

    class Meta:
        model = AdminUser
        fields = ['id', 'email', 'full_name', 'role_name', 'permissions',
                  'is_active', 'created_by_name', 'created_at']

    def get_permissions(self, obj):
        return list(obj.permissions.values_list('name', flat=True))


class ChangeAdminPermissionSerializerIn(serializers.Serializer):
    """Serializer for changing admin permissions"""
    admin_user_id = serializers.UUIDField()
    role = serializers.ChoiceField(choices=['checker', 'verifier', 'approver'], required=False)
    permissions = serializers.ListField(
        child=serializers.ChoiceField(choices=ADMIN_PERMISSION_CHOICES),
        required=False
    )

    def create(self, validated_data):
        admin_user_id = validated_data.get("admin_user_id")
        role = validated_data.get("role")
        permissions = validated_data.get("permissions", [])

        # Get current admin user from context
        current_admin = self.context['admin_user']

        try:
            target_admin = AdminUser.objects.get(id=admin_user_id)
        except AdminUser.DoesNotExist:
            raise InvalidRequestException({"detail": "Admin user not found"})

        # Update role if provided
        if role:
            try:
                admin_role = AdminRole.objects.get(name=role)
                target_admin.role = admin_role
                target_admin.save()
            except AdminRole.DoesNotExist:
                raise InvalidRequestException({"detail": "Invalid role specified"})

        # Update permissions if provided
        if permissions:
            target_admin.permissions.clear()
            for perm_name in permissions:
                try:
                    permission = AdminPermission.objects.get(name=perm_name)
                    target_admin.permissions.add(permission)
                except AdminPermission.DoesNotExist:
                    pass

        # Log admin action
        log_admin_action(
            admin_user=current_admin,
            action='change_permissions',
            description=f"Changed permissions for admin user: {target_admin.user.get_full_name()}",
            target_model='AdminUser',
            target_id=str(target_admin.id),
            request=self.context['request']
        )

        return {
            "detail": f"Permissions updated for {target_admin.user.get_full_name()}",
            "data": AdminUserSerializerOut(target_admin).data
        }


class AdminPermissionSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = AdminPermission
        exclude = []


class CreateAdminUserSerializerIn(serializers.Serializer):
    """Serializer for creating new admin users"""
    email = serializers.EmailField()
    first_name = serializers.CharField(max_length=150)
    last_name = serializers.CharField(max_length=150)
    password = serializers.CharField(min_length=8)
    role = serializers.ChoiceField(choices=['checker', 'verifier', 'approver'])
    permissions = serializers.ListField(
        child=serializers.ChoiceField(choices=ADMIN_PERMISSION_CHOICES),
        required=False
    )

    def validate_email(self, value):
        if User.objects.filter(email=value).exists():
            raise InvalidRequestException({"detail": "User with this email already exists"})
        return value

    def validate_password(self, value):
        try:
            validate_password(value)
        except Exception as err:
            raise InvalidRequestException({"detail": str(err)})
        return value

    def create(self, validated_data):
        email = validated_data.get("email")
        first_name = validated_data.get("first_name")
        last_name = validated_data.get("last_name")
        password = validated_data.get("password")
        role_name = validated_data.get("role")
        permissions = validated_data.get("permissions", [])

        # Get current admin user from context
        current_admin = self.context['admin_user']

        # Create Django user
        user = User.objects.create_user(
            username=email,
            email=email,
            first_name=first_name,
            last_name=last_name,
            password=password
        )

        # Get or create admin role
        try:
            admin_role = AdminRole.objects.get(name=role_name)
        except AdminRole.DoesNotExist:
            raise InvalidRequestException({"detail": "Invalid role specified"})

        # Create admin user
        admin_user = AdminUser.objects.create(
            user=user,
            role=admin_role,
            created_by=current_admin
        )

        # Set permissions
        if permissions:
            for perm_name in permissions:
                try:
                    permission = AdminPermission.objects.get(name=perm_name)
                    admin_user.permissions.add(permission)
                except AdminPermission.DoesNotExist:
                    pass

        # Send email to the newly created admin user. State their login credentials, role and permissions
        email_subject = "Welcome to Bowen MFB Admin Portal"
        email_body = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h2 style="color: #28a745; margin-bottom: 20px;">🎉 Welcome to Bowen MFB Admin Portal!</h2>

                    <p>Dear <strong>{user.get_full_name()}</strong>,</p>

                    <p>Welcome to the Bowen MFB Admin Portal!</p>

                    <p>Your admin account has been successfully created with the following details:</p>

                    <ul>
                        <li><strong>Email:</strong> {user.email}</li>
                        <li><strong>Password:</strong> {password}</li>
                        <li><strong>Role:</strong> {admin_role.name}</li>
                        <li><strong>Permissions:</strong> {', '.join(permissions)}</li>
                    </ul>
                    
                    <p>Kindly ensure to change your password after logging in for the first time.</p>

                    <p>Please contact the system administrator if you have any questions or need assistance.</p>

                    <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                    <p style="color: #666; font-size: 14px;">
                        Best regards,<br>
                        <strong>Bowen MFB Team</strong>
                    </p>
                </div>
            </div>
        </body>
        </html>
        """

        send_notification_async(
            notification_type='email',
            recipient=user.email,
            message=email_body,
            subject=email_subject
        )

        return {
            "detail": f"Admin user {user.get_full_name()} created successfully",
            "data": AdminUserSerializerOut(admin_user).data
        }


class SetCustomerActiveSerializerIn(serializers.Serializer):
    """Serializer for setting customer as active"""
    customer_id = serializers.UUIDField()
    reason = serializers.CharField(max_length=500, required=False)

    def create(self, validated_data):
        customer_id = validated_data.get("customer_id")

        try:
            customer = Customer.objects.get(id=customer_id)
        except Customer.DoesNotExist:
            raise InvalidRequestException({"detail": "Customer not found"})

        customer.active = True
        customer.save()

        return {"detail": f"Customer {customer.user.get_full_name()} has been set as active"}


class BillPaymentRequestSerializerOut(serializers.ModelSerializer):
    """Serializer for bill payment request output"""
    company_name = serializers.CharField(source='company.name', allow_null=True)
    biller_name = serializers.CharField(source='biller.name', allow_null=True)
    biller_category_name = serializers.CharField(source='biller.category.name', allow_null=True)
    payment_item_name = serializers.CharField(source='payment_item.name', allow_null=True)
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', allow_null=True)

    class Meta:
        model = BillPaymentRequest
        fields = ['id', 'company_name', 'biller_name', 'biller_category_name',
                  'payment_item_name', 'amount', 'customer_id', 'customer_name',
                  'description', 'is_checked', 'is_verified', 'is_approved',
                  'is_declined', 'created_by_name', 'created_at']


class BillPaymentSerializerOut(serializers.ModelSerializer):
    """Serializer for executed bill payment output"""
    company_name = serializers.CharField(source='company.name', allow_null=True)

    class Meta:
        model = BillPayment
        fields = ['id', 'company_name', 'biller_name', 'biller_category_name',
                  'payment_item_name', 'customer_id', 'customer_name', 'amount',
                  'fee', 'status', 'reference', 'created_at']


class AdminAuditTrailSerializerOut(serializers.ModelSerializer):
    """Serializer for admin audit trail output"""
    admin_name = serializers.CharField(source='admin_user.user.get_full_name', allow_null=True)
    admin_email = serializers.CharField(source='admin_user.user.email', allow_null=True)
    action_display = serializers.CharField(source='get_action_display')

    class Meta:
        model = AdminAuditTrail
        fields = ['id', 'admin_name', 'admin_email', 'action', 'action_display',
                  'description', 'target_model', 'target_id', 'ip_address',
                  'user_agent', 'additional_data', 'created_at']


class AdminChangePasswordSerializerIn(serializers.Serializer):
    """Serializer for admin password change"""
    old_password = serializers.CharField()
    new_password = serializers.CharField(min_length=8)
    confirm_password = serializers.CharField()

    def validate_new_password(self, value):
        try:
            validate_password(value)
        except Exception as err:
            raise InvalidRequestException({"detail": str(err)})
        return value

    def validate(self, data):
        if data['new_password'] != data['confirm_password']:
            raise InvalidRequestException({"detail": "New passwords do not match"})

        if data['old_password'] == data['new_password']:
            raise InvalidRequestException({"detail": "New password cannot be the same as old password"})

        return data

    def create(self, validated_data):
        old_password = validated_data.get("old_password")
        new_password = validated_data.get("new_password")

        # Get admin user from context
        admin_user = self.context['admin_user']
        user = admin_user.user

        # Verify old password
        if not check_password(old_password, user.password):
            raise InvalidRequestException({"detail": "Incorrect old password"})

        # Set new password
        user.set_password(new_password)
        user.save()

        return {"detail": "Password changed successfully"}


class UpdateAccountTransferLimitSerializerIn(serializers.Serializer):
    """Serializer for updating account transfer limit"""
    monthly_transfer_limit = serializers.FloatField(min_value=10000.00)
    daily_transfer_limit = serializers.FloatField(min_value=500.00)

    def update(self, instance, validated_data):
        instance.monthly_transfer_limit = validated_data.get("monthly_transfer_limit", instance.monthly_transfer_limit)
        instance.daily_transfer_limit = validated_data.get("daily_transfer_limit", instance.daily_transfer_limit)
        instance.save()
        return {"detail": "Account transfer limit updated successfully"}


class AdminApproveCustomerSerializerIn(serializers.Serializer):
    """Serializer for approving customer"""
    customer_id = serializers.UUIDField()
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    action = serializers.ChoiceField(choices=['approve', 'decline'])
    decline_reason = serializers.CharField(required=False, allow_blank=True, max_length=500)

    def validate(self, attrs):
        action = attrs.get("action")
        if action == 'decline' and not attrs.get('decline_reason'):
            raise InvalidRequestException({"detail": "Decline reason is required when declining a customer"})
        return attrs

    def create(self, validated_data):
        customer_id = validated_data.get("customer_id")
        user = validated_data.get("user")
        action = validated_data.get("action")
        decline_reason = validated_data.get("decline_reason", "")

        try:
            customer = Customer.objects.get(id=customer_id)
        except Customer.DoesNotExist:
            raise InvalidRequestException({"detail": "Customer not found"})

        if customer.admin_approved:
            raise InvalidRequestException({"detail": "Customer has already been approved"})

        if not customer.is_verified:
            raise InvalidRequestException({"detail": "Customer email is pending verification"})

        if action == 'decline':
            # send email and sms to customer
            email_subject = "Your onboarding request to {company_name} was declined".format(company_name=str(customer.company.name).upper())
            email_body = f"""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                        <h2 style="color: #28a745; margin-bottom: 20px;">🎉 Welcome to Bowen MFB Admin Portal!</h2>

                        <p>Dear <strong>{customer.user.get_full_name()}</strong>,</p>

                        <p>Your request to onboard to {customer.company.name} has been declined.</p>
                        
                        <p>Reason: {decline_reason}</p>
                        
                        <p>Please contact the bank support for more information.</p>

                        <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                        <p style="color: #666; font-size: 14px;">
                            Best regards,<br>
                            <strong>Bowen MFB Team</strong>
                        </p>
                    </div>
                </div>
            </body>
            </html>
            """

            # Send email notification
            send_notification_async(
                notification_type='email',
                recipient=customer.user.email,
                message=email_body,
                subject=email_subject
            )

            return {"detail": f"Customer: {customer.user.get_full_name()} has been declined", "data": CustomerSerializerOut(customer).data}

        customer.admin_approved = True
        customer.admin_approved_at = timezone.now()
        customer.admin_approved_by = user
        customer.save()

        return {"detail": f"Customer: {customer.user.get_full_name()} has been approved", "data": CustomerSerializerOut(customer).data}


class ActivateDeactivateAdminUserSerializerIn(serializers.Serializer):
    """Serializer for activating or deactivating admin user"""
    admin_user_id = serializers.UUIDField()
    action = serializers.ChoiceField(choices=['activate', 'deactivate'])

    def create(self, validated_data):
        admin_user_id = validated_data.get("admin_user_id")
        action = validated_data.get("action")

        current_admin = self.context['admin_user']

        try:
            admin_user = AdminUser.objects.get(id=admin_user_id)
        except AdminUser.DoesNotExist:
            raise InvalidRequestException({"detail": "Admin user not found"})

        if action == 'activate':
            admin_user.is_active = True
        else:
            admin_user.is_active = False
        admin_user.save()

        # Log admin action
        log_admin_action(
            admin_user=current_admin,
            action='change_permissions',
            description=str(action).upper() + "ed admin user: " + str(admin_user.user.get_full_name()),
            target_model='AdminUser',
            target_id=str(admin_user.id),
            request=self.context['request']
        )

        return {"detail": f"Admin user {admin_user.user.get_full_name()} has been {action}d successfully"}


