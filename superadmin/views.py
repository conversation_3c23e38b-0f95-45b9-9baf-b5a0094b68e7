from drf_spectacular.utils import extend_schema, OpenApiParameter
from rest_framework import status, generics
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from bowenmfb.modules.paginations import CustomPagination
from bowenmfb.modules.utils import get_superadmin_dashboard_data
from transfer.models import AccountSignatory, SignatoryHierarchy
from bowenmfb.modules.exceptions import raise_serializer_error_msg
from bowenmfb.modules.permissions import (
    IsAdminUser, CanViewCompanies, CanManageCompanyUsers,
    CanCheckAccountRequests, CanVerifyAccountRequests, CanApproveAccountRequests,
    CanViewTransfers, CanManageAdminPermissions, CanCreateAdminUsers,
    CanViewBillPayments, CanViewAuditTrail, CanUpdateTransferLimit, CanUpdateSignatories
)
from .serializers import *
from transfer.serializers import (
    AccountSignatorySerializerOut, AccountSignatorySerializerIn,
    SignatoryHierarchySerializerOut, SignatoryHierarchySerializerIn
)


class AdminLoginAPIView(APIView):
    permission_classes = []

    @extend_schema(request=AdminLoginSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = AdminLoginSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()

        # Log admin login
        try:
            from django.contrib.auth.models import User
            user = User.objects.get(email=request.data.get('email'))
            admin_user = AdminUser.objects.get(user=user, is_active=True)
            log_admin_action(
                admin_user=admin_user,
                action='login',
                description=f"Admin user logged in: {user.get_full_name()}",
                request=request
            )
        except (User.DoesNotExist, AdminUser.DoesNotExist):
            pass

        return Response(response)


class CompanyListAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated, IsAdminUser, CanViewCompanies]

    @extend_schema(
        parameters=[
            OpenApiParameter(name="search", type=str, required=False),
            OpenApiParameter(name="active", type=str, required=False),
            OpenApiParameter(name="date_from", type=str, description="filter date created: YYYY-MM-DD"),
            OpenApiParameter(name="date_to", type=str, description="filter date created: YYYY-MM-DD")
        ],
        responses={status.HTTP_200_OK: CompanySerializerOut(many=True)}
    )
    def get(self, request, company_id=None):
        if company_id:
            try:
                company = Company.objects.get(id=company_id)
                serializer = CompanySerializerOut(company)
                return Response({
                    "detail": "Company retrieved successfully",
                    "data": serializer.data
                })
            except Company.DoesNotExist:
                return Response({"detail": "Company not found"}, status=status.HTTP_404_NOT_FOUND)

        query = Q()
        search = request.query_params.get('search', '')
        active_filter = request.query_params.get('active', '')
        date_from = request.query_params.get('date_from', '')
        date_to = request.query_params.get('date_to', '')

        if search:
            query &= Q(name__icontains=search)

        if active_filter == "true":
            query &= Q(active=True)

        if active_filter == "false":
            query &= Q(active=False)

        if date_from and date_to:
            query &= Q(created_at__gte=date_from, created_at__lte=date_to)

        queryset = Company.objects.filter(query).order_by('-created_at')
        companies = self.paginate_queryset(queryset, request)
        serializer = CompanySerializerOut(companies, many=True)
        return Response({
            "detail": "Companies retrieved successfully",
            "data": self.get_paginated_response(serializer.data).data
        })


class CompanyCustomersAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser, CanViewCompanies]

    @extend_schema(responses={status.HTTP_200_OK: CustomerSerializerOut(many=True)})
    def get(self, request, company_id):
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response({"detail": "Company not found"}, status=status.HTTP_404_NOT_FOUND)

        customers = Customer.objects.filter(company=company).order_by('-created_at')
        serializer = CustomerSerializerOut(customers, many=True)
        return Response({
            "detail": "Company customers retrieved successfully",
            "company": company.name,
            "data": serializer.data
        })


class SetCustomerInactiveAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser, CanManageCompanyUsers]

    @extend_schema(request=SetCustomerInactiveSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        # Get admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        serializer = SetCustomerInactiveSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='set_customer_inactive',
            description=f"Set customer as inactive: {request.data.get('customer_id')}",
            target_model='Customer',
            target_id=str(request.data.get('customer_id')),
            request=request
        )

        return Response(response)


class AccountCreationRequestListAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated, IsAdminUser, CanCheckAccountRequests]

    @extend_schema(
        parameters=[
            OpenApiParameter(name="status", type=str, required=False),
            OpenApiParameter(name="search", type=str, required=False),
            OpenApiParameter(name="date_from", type=str, description="filter date created: YYYY-MM-DD"),
            OpenApiParameter(name="date_to", type=str, description="filter date created: YYYY-MM-DD")
        ],
        responses={status.HTTP_200_OK: AccountCreationRequestSerializerOut(many=True)}
    )
    def get(self, request, pk=None):
        # Filter by status if provided
        status_filter = request.query_params.get('status', None)
        search = request.query_params.get('search', '')
        date_from = request.query_params.get('date_from', '')
        date_to = request.query_params.get('date_to', '')

        if pk:
            try:
                request = AccountCreationRequest.objects.get(id=pk)
                serializer = AccountCreationRequestSerializerOut(request)
                return Response({
                    "detail": "Account creation request retrieved successfully",
                    "data": serializer.data
                })
            except AccountCreationRequest.DoesNotExist:
                return Response({"detail": "Account creation request not found"}, status=status.HTTP_404_NOT_FOUND)

        query = Q()
        if search:
            query &= (
                    Q(creation_request__business_name__icontains=search) | Q(creation_request__business_email__icontains=search) | Q(
                creation_request__business_registration_number__icontains=search)
            )

        if date_from and date_to:
            query &= Q(created_at__gte=date_from, created_at__lte=date_to)

        if status_filter:
            query &= Q(status=status_filter)

        requests = AccountCreationRequest.objects.filter(query).order_by('-created_at')
        queryset = self.paginate_queryset(requests, request)
        serializer = AccountCreationRequestSerializerOut(queryset, many=True)
        return Response({
            "detail": "Account creation requests retrieved successfully",
            "data": self.get_paginated_response(serializer.data).data
        })


class ProcessAccountRequestAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser]

    def get_permissions(self):
        permissions = [IsAuthenticated, IsAdminUser]

        if hasattr(self, 'request') and self.request.data:
            action = self.request.data.get('action')
            if action == 'check':
                permissions.append(CanCheckAccountRequests)
            elif action == 'verify':
                permissions.append(CanVerifyAccountRequests)
            elif action in ['approve', 'decline']:
                permissions.append(CanApproveAccountRequests)

        return [permission() for permission in permissions]

    @extend_schema(request=ProcessAccountRequestSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        # Get admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        serializer = ProcessAccountRequestSerializerIn(
            data=request.data,
            context={'admin_user': admin_user}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class TransferListAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser, CanViewTransfers]

    @extend_schema(responses={status.HTTP_200_OK})
    def get(self, request):
        # Get query parameters
        company_id = request.query_params.get('company_id', None)
        transfer_type = request.query_params.get('type', 'all')  # 'single', 'bulk', or 'all'

        response_data = {}

        # Filter single transfers
        single_transfers = SingleTransferRequest.objects.all()
        if company_id:
            single_transfers = single_transfers.filter(company_id=company_id)

        # Filter bulk transfers
        bulk_transfers = BulkTransferRequest.objects.all()
        if company_id:
            bulk_transfers = bulk_transfers.filter(company_id=company_id)

        if transfer_type == 'single':
            single_serializer = SingleTransferSerializerOut(single_transfers.order_by('-created_at'), many=True)
            response_data = {
                "detail": "Single transfers retrieved successfully",
                "single_transfers": single_serializer.data
            }
        elif transfer_type == 'bulk':
            bulk_serializer = BulkTransferSerializerOut(bulk_transfers.order_by('-created_at'), many=True)
            response_data = {
                "detail": "Bulk transfers retrieved successfully",
                "bulk_transfers": bulk_serializer.data
            }
        else:  # 'all'
            single_serializer = SingleTransferSerializerOut(single_transfers.order_by('-created_at'), many=True)
            bulk_serializer = BulkTransferSerializerOut(bulk_transfers.order_by('-created_at'), many=True)
            response_data = {
                "detail": "All transfers retrieved successfully",
                "single_transfers": single_serializer.data,
                "bulk_transfers": bulk_serializer.data
            }

        return Response(response_data)


class AdminUserListAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated, IsAdminUser, CanManageAdminPermissions]

    @extend_schema(
        parameters=[
            OpenApiParameter(name="search", type=str, required=False),
            OpenApiParameter(name="active", type=str, required=False),
            OpenApiParameter(name="date_from", type=str, description="filter date created: YYYY-MM-DD"),
            OpenApiParameter(name="date_to", type=str, description="filter date created: YYYY-MM-DD")
        ],
        responses={status.HTTP_200_OK: AdminUserSerializerOut(many=True)}
    )
    def get(self, request, pk=None):
        search = request.query_params.get('search', '')
        active_filter = request.query_params.get('active', '')
        date_from = request.query_params.get('date_from', '')
        date_to = request.query_params.get('date_to', '')

        if pk:
            try:
                admin_user = AdminUser.objects.get(id=pk)
                serializer = AdminUserSerializerOut(admin_user)
                return Response({
                    "detail": "Admin user retrieved successfully",
                    "data": serializer.data
                })
            except AdminUser.DoesNotExist:
                return Response({"detail": "Admin user not found"}, status=status.HTTP_404_NOT_FOUND)

        query = Q()
        if search:
            query &= (
                    Q(user__first_name__icontains=search) | Q(user__last_name__icontains=search) | Q(user__email__icontains=search)
            )

        if active_filter == "true":
            query &= Q(is_active=True)
        elif active_filter == "false":
            query &= Q(is_active=False)

        if date_from and date_to:
            query &= Q(created_at__gte=date_from, created_at__lte=date_to)

        admin_users = AdminUser.objects.filter(query).order_by('-created_at')
        queryset = self.paginate_queryset(admin_users, request)
        serializer = AdminUserSerializerOut(queryset, many=True)
        return Response({
            "detail": "Admin users retrieved successfully",
            "data": self.get_paginated_response(serializer.data).data
        })


class ActivateDeactivateAdminUserAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser, CanManageAdminPermissions]

    @extend_schema(request=ActivateDeactivateAdminUserSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        # Get current admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        serializer = ActivateDeactivateAdminUserSerializerIn(
            data=request.data,
            context={'admin_user': admin_user, 'request': request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class ChangeAdminPermissionAPIView(APIView):
    """Change admin user permissions - requires manage_admin_permissions permission"""
    permission_classes = [IsAuthenticated, IsAdminUser, CanManageAdminPermissions]

    @extend_schema(request=ChangeAdminPermissionSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        # Get current admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        serializer = ChangeAdminPermissionSerializerIn(
            data=request.data,
            context={'admin_user': admin_user, 'request': request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class AdminDashboardAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser]

    @extend_schema(responses={status.HTTP_200_OK})
    def get(self, request):
        # Get admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        # Get comprehensive dashboard data
        dashboard_data = get_superadmin_dashboard_data()

        # Add admin info to the dashboard data
        dashboard_data["admin_info"] = {
            "name": admin_user.user.get_full_name(),
            "role": admin_user.role.name if admin_user.role else None,
            "permissions": list(admin_user.permissions.values_list('name', flat=True))
        }

        return Response({
            "detail": "Dashboard data retrieved successfully",
            "data": dashboard_data
        })


class ListAdminPermissionListAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated, CanManageAdminPermissions]
    queryset = AdminPermission.objects.all().order_by("-name")
    serializer_class = AdminPermissionSerializerOut


class CreateAdminUserAPIView(APIView):
    """Create new admin user - requires create_admin_users permission"""
    permission_classes = [IsAuthenticated, IsAdminUser, CanCreateAdminUsers]

    @extend_schema(request=CreateAdminUserSerializerIn, responses={status.HTTP_201_CREATED})
    def post(self, request):
        # Get current admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        serializer = CreateAdminUserSerializerIn(
            data=request.data,
            context={'admin_user': admin_user}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='create_admin',
            description=f"Created new admin user: {response['data']['full_name']}",
            target_model='AdminUser',
            target_id=response['data']['id'],
            request=request
        )

        return Response(response, status=status.HTTP_201_CREATED)


class CustomerListAPIView(APIView, CustomPagination):
    """List all customers with search functionality"""
    permission_classes = [IsAuthenticated, IsAdminUser, CanViewCompanies]

    @extend_schema(
        parameters=[
            OpenApiParameter(name="search", type=str), OpenApiParameter(name="company_id", type=str), OpenApiParameter(name="active", type=str),
            OpenApiParameter(name="email_verified", type=str), OpenApiParameter(name="admin_approved", type=str),
            OpenApiParameter(name="date_from", type=str, description="filter date created: YYYY-MM-DD"),
            OpenApiParameter(name="date_to", type=str, description="filter date created: YYYY-MM-DD")
        ],
        responses={status.HTTP_200_OK: CustomerSerializerOut(many=True)}
    )
    def get(self, request):
        # Get admin user for logging
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        search = request.query_params.get('search', '')
        company_id = request.query_params.get('company_id', '')
        active_filter = request.query_params.get('active', '')
        verified_filter = request.query_params.get('email_verified', '')
        admin_approved_filter = request.query_params.get('admin_approved', '')

        # Base queryset
        customers = Customer.objects.all()

        # Apply search filter
        if search:
            customers = customers.filter(
                Q(user__first_name__icontains=search) |
                Q(user__last_name__icontains=search) |
                Q(user__email__icontains=search) |
                Q(phone_number__icontains=search) |
                Q(company__name__icontains=search)
            )

        # Apply company filter
        if company_id:
            customers = customers.filter(company_id=company_id)

        # Apply active filter
        if active_filter.lower() == 'true':
            customers = customers.filter(active=True)
        elif active_filter.lower() == 'false':
            customers = customers.filter(active=False)

        # Apply verified filter
        if verified_filter.lower() == 'true':
            customers = customers.filter(is_verified=True)
        elif verified_filter.lower() == 'false':
            customers = customers.filter(is_verified=False)

        # Apply admin approved filter
        if admin_approved_filter.lower() == 'true':
            customers = customers.filter(admin_approved=True)
        elif admin_approved_filter.lower() == 'false':
            customers = customers.filter(admin_approved=False)

        # Apply date filter
        date_from = request.query_params.get('date_from', '')
        date_to = request.query_params.get('date_to', '')
        if date_from and date_to:
            customers = customers.filter(created_at__gte=date_from, created_at__lte=date_to)

        customers = customers.order_by('-created_at')
        queryset = self.paginate_queryset(customers, request)
        serializer = CustomerSerializerOut(queryset, many=True)

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='view_customers',
            description=f"Viewed customers list with filters: search='{search}', company_id='{company_id}'",
            request=request,
            additional_data={
                'search': search,
                'company_id': company_id,
                'active_filter': active_filter,
                'verified_filter': verified_filter,
                'total_results': customers.count()
            }
        )

        return Response({
            "detail": "Customers retrieved successfully",
            "total": customers.count(),
            "data": self.get_paginated_response(serializer.data).data
        })


class SetCustomerActiveAPIView(APIView):
    """Set customer as active - complement to existing inactive functionality"""
    permission_classes = [IsAuthenticated, IsAdminUser, CanManageCompanyUsers]

    @extend_schema(request=SetCustomerActiveSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        # Get admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        serializer = SetCustomerActiveSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='set_customer_active',
            description=f"Set customer as active: {request.data.get('customer_id')}",
            target_model='Customer',
            target_id=str(request.data.get('customer_id')),
            request=request
        )

        return Response(response)


class EnhancedTransferListAPIView(APIView, CustomPagination):
    """Enhanced transfer list with search functionality"""
    permission_classes = [IsAuthenticated, IsAdminUser, CanViewTransfers]

    @extend_schema(
        parameters=[
            OpenApiParameter(name="company_id", type=str, required=False),
            OpenApiParameter(name="type", type=str, required=True,
                             description="Select between single or bulk transfers or all. Choices are 'single', 'bulk' and 'all'"),
            OpenApiParameter(name="search", type=str, required=False),
            OpenApiParameter(name="status", type=str, required=False),
            OpenApiParameter(name="date_from", type=str, description="filter date created: YYYY-MM-DD"),
            OpenApiParameter(name="date_to", type=str, description="filter date created: YYYY-MM-DD")
        ],
        responses={status.HTTP_200_OK}
    )

    def get(self, request):
        # Get admin user for logging
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        company_id = request.query_params.get('company_id', None)
        transfer_type = request.query_params.get('type', 'all')  # 'single', 'bulk', or 'all'
        search = request.query_params.get('search', '')
        status_filter = request.query_params.get('status', '')  # 'pending', 'approved', 'declined'
        date_from = request.query_params.get('date_from', '')
        date_to = request.query_params.get('date_to', '')

        response_data = {}

        # Filter single transfers
        if transfer_type == 'single':

            single_transfers = SingleTransferRequest.objects.all()
            if company_id:
                single_transfers = single_transfers.filter(company_id=company_id)
            if search:
                single_transfers = single_transfers.filter(
                    Q(beneficiary_name__icontains=search) |
                    Q(beneficiary_account_number__icontains=search) |
                    Q(description__icontains=search) |
                    Q(company__name__icontains=search)
                )
            if status_filter == 'pending':
                single_transfers = single_transfers.filter(is_approved=False, is_declined=False)
            if status_filter == 'approved':
                single_transfers = single_transfers.filter(is_approved=True)
            if status_filter == 'declined':
                single_transfers = single_transfers.filter(is_declined=True)
            if date_from and date_to:
                single_transfers = single_transfers.filter(created_at__gte=date_from, created_at__lte=date_to)

            queryset = self.paginate_queryset(single_transfers.order_by('-created_at'), request)
            single_serializer = SingleTransferSerializerOut(queryset, many=True)
            response_data = {
                "detail": "Single transfers retrieved successfully",
                "total": single_transfers.count(),
                "single_transfers": self.get_paginated_response(single_serializer.data).data
            }

        elif transfer_type == 'bulk':

            # Filter bulk transfers
            bulk_transfers = BulkTransferRequest.objects.all()
            if company_id:
                bulk_transfers = bulk_transfers.filter(company_id=company_id)
            if search:
                bulk_transfers = bulk_transfers.filter(
                    Q(description__icontains=search) |
                    Q(company__name__icontains=search)
                )
            if status_filter == 'pending':
                bulk_transfers = bulk_transfers.filter(is_approved=False, is_declined=False)
            elif status_filter == 'approved':
                bulk_transfers = bulk_transfers.filter(is_approved=True)
            elif status_filter == 'declined':
                bulk_transfers = bulk_transfers.filter(is_declined=True)

            if date_from and date_to:
                bulk_transfers = bulk_transfers.filter(created_at__gte=date_from, created_at__lte=date_to)

            queryset = self.paginate_queryset(bulk_transfers.order_by('-created_at'), request)
            bulk_serializer = BulkTransferSerializerOut(queryset, many=True)
            response_data = {
                "detail": "Bulk transfers retrieved successfully",
                "total": bulk_transfers.count(),
                "bulk_transfers": self.get_paginated_response(bulk_serializer.data).data
            }

        else:  # 'all'
            single_transfers = SingleTransferRequest.objects.all().order_by('-created_at')[:50]
            bulk_transfers = BulkTransferRequest.objects.all().order_by('-created_at')[:50]
            single_serializer = SingleTransferSerializerOut(single_transfers, many=True)
            bulk_serializer = BulkTransferSerializerOut(bulk_transfers, many=True)
            response_data = {
                "detail": "All transfers retrieved successfully",
                "single_total": SingleTransferRequest.objects.count(),
                "bulk_total": BulkTransferRequest.objects.count(),
                "single_transfers": single_serializer.data,
                "bulk_transfers": bulk_serializer.data
            }

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='view_transfers',
            description=f"Viewed transfers with filters: type='{transfer_type}', company_id='{company_id}', search='{search}'",
            request=request,
            additional_data={
                'transfer_type': transfer_type,
                'company_id': company_id,
                'search': search,
                'status_filter': status_filter
            }
        )

        return Response(response_data)


class BillPaymentListAPIView(APIView, CustomPagination):
    """List all bill payments with search and filtering functionality"""
    permission_classes = [IsAuthenticated, IsAdminUser, CanViewBillPayments]

    @extend_schema(
        parameters=[
            OpenApiParameter(name="company_id", type=str, required=False),
            OpenApiParameter(name="type", type=str, required=True,
                             description="Select between request or payments transfers or all. Choices are 'requests', 'payments' and 'all'"),
            OpenApiParameter(name="search", type=str, required=False),
            OpenApiParameter(name="status", type=str, required=False),
            OpenApiParameter(name="date_from", type=str, description="filter date created: YYYY-MM-DD"),
            OpenApiParameter(name="date_to", type=str, description="filter date created: YYYY-MM-DD")
        ],
        responses={status.HTTP_200_OK}
    )
    def get(self, request):
        # Get admin user for logging
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        company_id = request.query_params.get('company_id', None)
        search = request.query_params.get('search', '')
        status_filter = request.query_params.get('status', '')
        payment_type = request.query_params.get('type', 'all')  # 'requests', 'payments', or 'all'
        date_from = request.query_params.get('date_from', '')
        date_to = request.query_params.get('date_to', '')

        response_data = {}
        total_amount = BillPayment.objects.all().aggregate(total=Sum('amount'))['total'] or 0
        total_count = BillPayment.objects.count()
        # Filter bill payment requests
        if payment_type == 'requests':
            bill_requests = BillPaymentRequest.objects.all()
            if company_id:
                bill_requests = bill_requests.filter(company_id=company_id)
            if search:
                bill_requests = bill_requests.filter(
                    Q(customer_name__icontains=search) |
                    Q(customer_id__icontains=search) |
                    Q(biller__name__icontains=search) |
                    Q(company__name__icontains=search) |
                    Q(description__icontains=search)
                )
            if status_filter == 'pending':
                bill_requests = bill_requests.filter(is_approved=False, is_declined=False)
            elif status_filter == 'approved':
                bill_requests = bill_requests.filter(is_approved=True)
            elif status_filter == 'declined':
                bill_requests = bill_requests.filter(is_declined=True)
            if date_from and date_to:
                bill_requests = bill_requests.filter(created_at__gte=date_from, created_at__lte=date_to)
            queryset = self.paginate_queryset(bill_requests.order_by('-created_at'), request)
            requests_serializer = BillPaymentRequestSerializerOut(queryset, many=True)
            response_data = {
                "detail": "Bill payment requests retrieved successfully",
                "total": bill_requests.count(),
                "bill_payment_requests": self.get_paginated_response(requests_serializer.data).data
            }

        elif payment_type == 'payments':
            # Filter executed bill payments
            bill_payments = BillPayment.objects.all()
            if company_id:
                bill_payments = bill_payments.filter(company_id=company_id)
            if search:
                bill_payments = bill_payments.filter(
                    Q(customer_name__icontains=search) |
                    Q(customer_id__icontains=search) |
                    Q(biller_name__icontains=search) |
                    Q(company__name__icontains=search)
                )
            if status_filter:
                bill_payments = bill_payments.filter(status=status_filter)

            # Calculate totals
            total_amount = bill_payments.aggregate(total=Sum('amount'))['total'] or 0
            total_count = bill_payments.count()

            if date_from and date_to:
                bill_payments = bill_payments.filter(created_at__gte=date_from, created_at__lte=date_to)

            queryset = self.paginate_queryset(bill_payments.order_by('-created_at'), request)
            payments_serializer = BillPaymentSerializerOut(queryset, many=True)
            response_data = {
                "detail": "Bill payments retrieved successfully",
                "total_count": total_count,
                "total_amount": total_amount,
                "bill_payments": self.get_paginated_response(payments_serializer.data).data
            }

        else:  # 'all'
            bill_requests = BillPaymentRequest.objects.all().order_by('-created_at')[:50]
            bill_payments = BillPayment.objects.all().order_by('-created_at')[:50]
            requests_serializer = BillPaymentRequestSerializerOut(bill_requests, many=True)
            payments_serializer = BillPaymentSerializerOut(bill_payments, many=True)
            response_data = {
                "detail": "All bill payment data retrieved successfully",
                "requests_total": BillPaymentRequest.objects.count(),
                "payments_total_count": BillPayment.objects.count(),
                "payments_total_amount": BillPayment.objects.all().aggregate(total=Sum('amount'))['total'] or 0,
                "bill_payment_requests": requests_serializer.data,
                "bill_payments": payments_serializer.data
            }

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='view_bill_payments',
            description=f"Viewed bill payments with filters: type='{payment_type}', company_id='{company_id}', search='{search}'",
            request=request,
            additional_data={
                'payment_type': payment_type,
                'company_id': company_id,
                'search': search,
                'status_filter': status_filter,
                'total_amount': total_amount,
                'total_count': total_count
            }
        )

        return Response(response_data)


class AuditTrailListAPIView(APIView, CustomPagination):
    """List admin audit trail with filtering"""
    permission_classes = [IsAuthenticated, IsAdminUser, CanViewAuditTrail]

    @extend_schema(
        parameters=[
            OpenApiParameter(name="admin_id", type=str, required=False),
            OpenApiParameter(name="action", type=str, required=True),
            OpenApiParameter(name="search", type=str, required=False),
            OpenApiParameter(name="start_date", type=str, required=False),
            OpenApiParameter(name="end_date", type=str, required=False)
        ],
        responses={status.HTTP_200_OK: AdminAuditTrailSerializerOut(many=True)}
    )
    def get(self, request):
        # Get admin user for logging
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        admin_id = request.query_params.get('admin_id', '')
        action = request.query_params.get('action', '')
        search = request.query_params.get('search', '')
        start_date = request.query_params.get('start_date', '')
        end_date = request.query_params.get('end_date', '')

        # Base queryset
        audit_trails = AdminAuditTrail.objects.all()

        # Apply filters
        if admin_id:
            audit_trails = audit_trails.filter(admin_user_id=admin_id)

        if action:
            audit_trails = audit_trails.filter(action=action)

        if search:
            audit_trails = audit_trails.filter(
                Q(ip_address__icontains=search) |
                Q(admin_user__user__first_name__icontains=search) |
                Q(admin_user__user__last_name__icontains=search) |
                Q(admin_user__user__email__icontains=search)
            )

        if start_date:
            try:
                from datetime import datetime
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
                audit_trails = audit_trails.filter(created_at__gte=start_date_obj)
            except ValueError:
                pass

        if end_date:
            try:
                from datetime import datetime
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
                audit_trails = audit_trails.filter(created_at__lte=end_date_obj)
            except ValueError:
                pass

        audit_trails = audit_trails.order_by('-created_at')
        queryset = self.paginate_queryset(audit_trails, request)
        serializer = AdminAuditTrailSerializerOut(queryset, many=True)

        # Log admin action (but don't create infinite loop)
        if action != 'view_audit_trail':  # Prevent logging when viewing audit trail
            log_admin_action(
                admin_user=admin_user,
                action='view_audit_trail',
                description=f"Viewed audit trail with filters: admin_id='{admin_id}', action='{action}', search='{search}'",
                request=request,
                additional_data={
                    'admin_id': admin_id,
                    'action': action,
                    'search': search,
                    'start_date': start_date,
                    'end_date': end_date,
                    'total_results': audit_trails.count()
                }
            )

        return Response({
            "detail": "Audit trail retrieved successfully",
            "total": audit_trails.count(),
            "data": self.get_paginated_response(serializer.data).data
        })


class AdminChangePasswordAPIView(APIView):
    """Change admin user password"""
    permission_classes = [IsAuthenticated, IsAdminUser]

    @extend_schema(request=AdminChangePasswordSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        # Get admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        serializer = AdminChangePasswordSerializerIn(
            data=request.data,
            context={'admin_user': admin_user}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='change_password',
            description="Changed login password",
            target_model='User',
            target_id=str(admin_user.user.id),
            request=request
        )

        return Response(response)


class UpdateTransferLimitAPIView(APIView):
    """Update account transfer limit"""
    permission_classes = [IsAuthenticated, IsAdminUser, CanUpdateTransferLimit]

    @extend_schema(request=UpdateAccountTransferLimitSerializerIn, responses={status.HTTP_200_OK})
    def put(self, request, account_id):
        # Get admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        try:
            account = CompanyAccount.objects.get(id=account_id)
        except CompanyAccount.DoesNotExist:
            raise InvalidRequestException({"detail": "Account not found"})

        serializer = UpdateAccountTransferLimitSerializerIn(data=request.data, instance=account)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()

        log_admin_action(
            admin_user=admin_user,
            action='update_transfer_limit',
            description=f"Updated transfer limit for account {account_id}",
            target_model='CompanyAccount',
            target_id=str(account_id),
            request=request
        )

        return Response(response)


class AccountSignatoryListAPIView(generics.ListCreateAPIView):
    """
    List and create account signatories for company accounts.
    SuperAdmin can manage signatories across all companies.
    """
    permission_classes = [IsAuthenticated, IsAdminUser, CanUpdateSignatories]
    serializer_class = AccountSignatorySerializerOut
    pagination_class = CustomPagination

    def get_queryset(self):
        # SuperAdmin can view signatories from all companies
        account_id = self.request.query_params.get('account_id')
        company_id = self.request.query_params.get('company_id')

        queryset = AccountSignatory.objects.all()

        if account_id:
            queryset = queryset.filter(company_account_id=account_id)
        elif company_id:
            queryset = queryset.filter(company_account__company_id=company_id)

        return queryset.order_by('-created_at')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return AccountSignatorySerializerIn
        return AccountSignatorySerializerOut

    @extend_schema(
        parameters=[
            OpenApiParameter(name="account_id", type=str, required=False),
            OpenApiParameter(name="company_id", type=str, required=False)
        ],
        responses={status.HTTP_200_OK: AccountSignatorySerializerOut(many=True)}
    )
    def get(self, request, *args, **kwargs):
        # Get admin user for logging
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        response = super().get(request, *args, **kwargs)

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='view_signatories',
            description=f"Viewed account signatories with filters: account_id='{request.query_params.get('account_id', '')}', company_id='{request.query_params.get('company_id', '')}'",
            request=request,
            additional_data={
                'account_id': request.query_params.get('account_id'),
                'company_id': request.query_params.get('company_id')
            }
        )

        return response

    @extend_schema(request=AccountSignatorySerializerIn, responses={status.HTTP_201_CREATED})
    def post(self, request, *args, **kwargs):
        # Get admin user for logging
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        # SuperAdmin can create signatories for any company account
        account_id = request.data.get('company_account')
        if not CompanyAccount.objects.filter(id=account_id).exists():
            raise InvalidRequestException({"detail": "Invalid account selection"})

        response = super().post(request, *args, **kwargs)

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='create_signatory',
            description=f"Created account signatory for account {account_id}",
            target_model='AccountSignatory',
            target_id=str(response.data.get('id')) if hasattr(response, 'data') else None,
            request=request,
            additional_data={
                'account_id': account_id,
                'customer_id': request.data.get('customer'),
                'permissions': {
                    'can_upload': request.data.get('can_upload'),
                    'can_check': request.data.get('can_check'),
                    'can_verify': request.data.get('can_verify'),
                    'can_approve': request.data.get('can_approve')
                }
            }
        )

        return response


class AccountSignatoryRetrieveUpdateDestroyAPIView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, and delete account signatories.
    SuperAdmin can manage signatories across all companies.
    """
    permission_classes = [IsAuthenticated, IsAdminUser, CanUpdateSignatories]
    serializer_class = AccountSignatorySerializerOut
    lookup_field = "id"

    def get_queryset(self):
        # SuperAdmin can access signatories from all companies
        return AccountSignatory.objects.all()

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return AccountSignatorySerializerIn
        return AccountSignatorySerializerOut

    @extend_schema(responses={status.HTTP_200_OK: AccountSignatorySerializerOut})
    def get(self, request, *args, **kwargs):
        # Get admin user for logging
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        response = super().get(request, *args, **kwargs)

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='view_signatory',
            description=f"Viewed account signatory details: {kwargs.get('id')}",
            target_model='AccountSignatory',
            target_id=str(kwargs.get('id')),
            request=request
        )

        return response

    @extend_schema(request=AccountSignatorySerializerIn, responses={status.HTTP_200_OK})
    def put(self, request, *args, **kwargs):
        # Get admin user for logging
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        response = super().put(request, *args, **kwargs)

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='update_signatory',
            description=f"Updated account signatory: {kwargs.get('id')}",
            target_model='AccountSignatory',
            target_id=str(kwargs.get('id')),
            request=request,
            additional_data={
                'updated_permissions': {
                    'can_upload': request.data.get('can_upload'),
                    'can_check': request.data.get('can_check'),
                    'can_verify': request.data.get('can_verify'),
                    'can_approve': request.data.get('can_approve')
                }
            }
        )

        return response

    @extend_schema(responses={status.HTTP_204_NO_CONTENT})
    def delete(self, request, *args, **kwargs):
        # Get admin user for logging
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        # Get signatory details before deletion for logging
        signatory = self.get_object()
        signatory_info = {
            'customer_name': signatory.customer.user.get_full_name(),
            'account_number': signatory.company_account.account_number,
            'company_name': signatory.company_account.company.name
        }

        response = super().delete(request, *args, **kwargs)

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='delete_signatory',
            description=f"Deleted account signatory: {kwargs.get('id')}",
            target_model='AccountSignatory',
            target_id=str(kwargs.get('id')),
            request=request,
            additional_data=signatory_info
        )

        return response


class SignatoryHierarchyAPIView(APIView):
    """
    Manage signatory hierarchy for company accounts.
    SuperAdmin can manage hierarchies across all companies.
    """
    permission_classes = [IsAuthenticated, IsAdminUser, CanUpdateSignatories]

    @extend_schema(
        parameters=[OpenApiParameter(name="account_id", type=str, required=True)],
        responses={status.HTTP_200_OK: SignatoryHierarchySerializerOut}
    )
    def get(self, request):
        # Get admin user for logging
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        account_id = request.GET.get("account_id")

        try:
            # SuperAdmin can access any company account
            account = CompanyAccount.objects.get(id=account_id)
            hierarchy, created = SignatoryHierarchy.objects.get_or_create(
                company_account=account,
                defaults={'total_levels': 1, 'is_single_signatory': False}
            )
            serializer = SignatoryHierarchySerializerOut(hierarchy)

            # Log admin action
            log_admin_action(
                admin_user=admin_user,
                action='view_signatory_hierarchy',
                description=f"Viewed signatory hierarchy for account: {account_id}",
                target_model='SignatoryHierarchy',
                target_id=str(hierarchy.id),
                request=request,
                additional_data={
                    'account_id': account_id,
                    'company_name': account.company.name,
                    'created_new': created
                }
            )

            return Response({
                "detail": "Signatory hierarchy retrieved successfully",
                "data": serializer.data
            })
        except CompanyAccount.DoesNotExist:
            raise InvalidRequestException({"detail": "Account not found"})

    @extend_schema(request=SignatoryHierarchySerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        # Get admin user for logging
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        account_id = request.data.get("account_id")

        try:
            # SuperAdmin can access any company account
            account = CompanyAccount.objects.get(id=account_id)
            hierarchy, created = SignatoryHierarchy.objects.get_or_create(
                company_account=account
            )

            serializer = SignatoryHierarchySerializerIn(hierarchy, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()

                # Log admin action
                log_admin_action(
                    admin_user=admin_user,
                    action='update_signatory_hierarchy',
                    description=f"Updated signatory hierarchy for account: {account_id}",
                    target_model='SignatoryHierarchy',
                    target_id=str(hierarchy.id),
                    request=request,
                    additional_data={
                        'account_id': account_id,
                        'company_name': account.company.name,
                        'hierarchy_settings': {
                            'total_levels': request.data.get('total_levels'),
                            'requires_checker': request.data.get('requires_checker'),
                            'requires_verifier': request.data.get('requires_verifier'),
                            'requires_approver': request.data.get('requires_approver'),
                            'is_single_signatory': request.data.get('is_single_signatory')
                        }
                    }
                )

                return Response({
                    "detail": "Signatory hierarchy updated successfully",
                    "data": SignatoryHierarchySerializerOut(hierarchy).data
                })
            else:
                raise InvalidRequestException({"detail": serializer.errors})
        except CompanyAccount.DoesNotExist:
            raise InvalidRequestException({"detail": "Account not found"})


class AdminApproveCustomerAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser, CanManageCompanyUsers]

    @extend_schema(request=AdminApproveCustomerSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        serializer = AdminApproveCustomerSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        # Log admin action
        data = response.get('data')

        log_admin_action(
            admin_user=admin_user,
            action='approve_customer',
            description=f"Approved customer: {data.get('full_name')} with email {data.get('email')}",
            target_model='Customer',
            target_id=str(request.data.get('customer_id')),
            request=request
        )

        return Response(response)

