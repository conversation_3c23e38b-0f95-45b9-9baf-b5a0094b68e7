"""
Django signals for superadmin notifications.

This module handles sending notifications for admin-related events.
"""

from django.db.models.signals import post_save
from django.dispatch import receiver
from django.db import transaction

from bowenmfb.modules.utils import send_notification_async, log_request
from .models import AccountCreationRequest, AdminUser


@receiver(post_save, sender=AccountCreationRequest)
def notify_admins_of_new_account_request(sender, instance, created, **kwargs):
    """Notify all admin users when a new account creation request is submitted."""
    if created:
        # Use transaction.on_commit to ensure the notification is sent after the transaction commits
        transaction.on_commit(lambda: send_account_request_notifications(instance))


def send_account_request_notifications(account_request):
    """Send notifications to all admin users about new account creation request."""
    try:
        # Get all active admin users
        admin_users = AdminUser.objects.filter(is_active=True)
        
        if not admin_users.exists():
            log_request("No active admin users found to notify about account creation request")
            return
        
        business_name = account_request.creation_request.business_name
        business_email = account_request.creation_request.business_email
        contact_person = account_request.creation_request.contact_person_name
        
        # Prepare notification messages
        sms_message = f"New account creation request from {business_name}. Contact: {contact_person}. Please review and take action."
        
        email_subject = "New Account Creation Request - Action Required"
        email_body = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h2 style="color: #dc3545; margin-bottom: 20px;">🚨 New Account Creation Request - Action Required</h2>

                    <p>Dear <strong>Admin</strong>,</p>

                    <p>A new account creation request has been submitted and requires your attention.</p>

                    <div style="background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #007bff;">
                        <h3 style="color: #004085; margin-top: 0;">Company Details</h3>
                        <table style="width: 100%; border-collapse: collapse;">
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; width: 35%;">Business Name:</td>
                                <td style="padding: 8px 0; color: #2c5aa0; font-weight: bold;">{business_name}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold;">Business Email:</td>
                                <td style="padding: 8px 0;">{business_email}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold;">Contact Person:</td>
                                <td style="padding: 8px 0;">{contact_person}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold;">Contact Phone:</td>
                                <td style="padding: 8px 0;">{account_request.creation_request.contact_person_phone}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold;">Business Address:</td>
                                <td style="padding: 8px 0;">{account_request.creation_request.business_address}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold;">Registration Number:</td>
                                <td style="padding: 8px 0;">{account_request.creation_request.business_registration_number}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold;">Registration Date:</td>
                                <td style="padding: 8px 0;">{account_request.creation_request.business_registration_date}</td>
                            </tr>
                        </table>
                    </div>

                    <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
                        <p style="margin: 0; color: #856404;">
                            <strong>Request Status:</strong> {account_request.get_status_display()}<br>
                            <strong>Submitted:</strong> {account_request.created_at.strftime('%Y-%m-%d %H:%M:%S')}
                        </p>
                    </div>

                    <div style="background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;">
                        <p style="margin: 0; color: #155724;">
                            <strong>Action Required:</strong> Please log into the admin portal to review and process this request.
                        </p>
                    </div>

                    <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                    <p style="color: #666; font-size: 14px;">
                        Best regards,<br>
                        <strong>Bowen MFB System</strong>
                    </p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Send notifications to all admin users
        for admin_user in admin_users:
            try:
                # Send SMS notification
                # send_notification_async(
                #     notification_type='sms',
                #     recipient=admin_user.user.username,  # Assuming username is phone number
                #     message=sms_message
                # )
                
                # Send email notification
                send_notification_async(
                    notification_type='email',
                    recipient=admin_user.user.email,
                    message=email_body,
                    subject=email_subject
                )
                
            except Exception as e:
                log_request(f"Failed to send notification to admin {admin_user.user.email}: {str(e)}")
        
        log_request(f"Sent account creation request notifications to {admin_users.count()} admin users for request {account_request.id}")
        
    except Exception as e:
        log_request(f"Failed to send account creation request notifications: {str(e)}")


# @receiver(post_save, sender=AdminUser)
# def notify_admin_user_created(sender, instance, created, **kwargs):
#     """Send welcome notification to newly created admin users."""
#     if created:
#         transaction.on_commit(lambda: send_admin_welcome_notification(instance))


# def send_admin_welcome_notification(admin_user):
#     """Send welcome notification to newly created admin user."""
#     try:
#         user = admin_user.user
#         role_name = admin_user.role.get_name_display() if admin_user.role else "No Role Assigned"
#         permissions = list(admin_user.permissions.values_list('name', flat=True))
#
#         # Prepare welcome messages
#         sms_message = f"Welcome to Bowen MFB Admin Portal! Your admin account has been created with role: {role_name}. Please check your email for details."
#
#         email_subject = "Welcome to Bowen MFB Admin Portal"
#         email_body = f"""
#         <html>
#         <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
#             <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
#                 <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
#                     <h2 style="color: #28a745; margin-bottom: 20px;">🎉 Welcome to Bowen MFB Admin Portal!</h2>
#
#                     <p>Dear <strong>{user.get_full_name()}</strong>,</p>
#
#                     <p>Welcome to the Bowen MFB Admin Portal!</p>
#
#                     <p>Your admin account has been successfully created with the following details:</p>
#
#                     <div style="background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;">
#                         <h3 style="color: #155724; margin-top: 0;">Account Information</h3>
#                         <table style="width: 100%; border-collapse: collapse;">
#                             <tr>
#                                 <td style="padding: 8px 0; font-weight: bold; width: 25%;">Name:</td>
#                                 <td style="padding: 8px 0;">{user.get_full_name()}</td>
#                             </tr>
#                             <tr>
#                                 <td style="padding: 8px 0; font-weight: bold;">Email:</td>
#                                 <td style="padding: 8px 0; color: #2c5aa0; font-weight: bold;">{user.email}</td>
#                             </tr>
#                             <tr>
#                                 <td style="padding: 8px 0; font-weight: bold;">Role:</td>
#                                 <td style="padding: 8px 0;">{role_name}</td>
#                             </tr>
#                             <tr>
#                                 <td style="padding: 8px 0; font-weight: bold;">Status:</td>
#                                 <td style="padding: 8px 0; color: #28a745; font-weight: bold;">Active</td>
#                             </tr>
#                         </table>
#                     </div>
#
#                     <div style="background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #007bff;">
#                         <h3 style="color: #004085; margin-top: 0;">Assigned Permissions</h3>
#                         <ul style="margin: 0; padding-left: 20px;">
#                             {chr(10).join([f"<li>{perm.replace('_', ' ').title()}</li>" for perm in permissions]) if permissions else "<li>No specific permissions assigned</li>"}
#                         </ul>
#                     </div>
#
#                     <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
#                         <p style="margin: 0; color: #856404;">
#                             <strong>Login Instructions:</strong> You can now log into the admin portal using your email address and the password provided to you separately.
#                         </p>
#                     </div>
#
#                     <p>Please contact your system administrator if you have any questions or need assistance.</p>
#
#                     <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">
#
#                     <p style="color: #666; font-size: 14px;">
#                         Best regards,<br>
#                         <strong>Bowen MFB Team</strong>
#                     </p>
#                 </div>
#             </div>
#         </body>
#         </html>
#         """
#
#         # Send SMS notification
#         # send_notification_async(
#         #     notification_type='sms',
#         #     recipient=user.username,  # Assuming username is phone number
#         #     message=sms_message
#         # )
#
#         # Send email notification
#         send_notification_async(
#             notification_type='email',
#             recipient=user.email,
#             message=email_body,
#             subject=email_subject
#         )
#
#         log_request(f"Sent welcome notification to new admin user {user.email}")
#
#     except Exception as e:
#         log_request(f"Failed to send admin welcome notification: {str(e)}")
