import json

from celery import shared_task
from django.utils import timezone

from account.models import AccountOfficer, Company, CompanyAccount
from bowenmfb.modules.utils import generate_transaction_reference, send_notification_async
from superadmin.models import AccountCreationRequest, AdminUser
from bowenmfb.modules.bankone import BankOneClient
client = BankOneClient()


@shared_task()
def create_corporate_account_with_bowen(request_id, user_id):
    """
    Call core banking to create account
    This will entail:
    1. Create Customer (i.e the signatories
    2. Create the Corporate Customer
    3. Create Account for the Corporate Customer

    Note: This will be passed to a scheduler for optimal performance
    """
    account_officer = AccountOfficer.objects.filter(name__isnull=False, email__isnull=False).order_by("?").first()
    account_officer_code = str(account_officer.code)
    account_request = AccountCreationRequest.objects.get(id=request_id)
    company_other_name = ""
    company_last_name = ""
    # company_bvn = ""
    company_gender = ""
    company_place_of_birth = ""
    company_date_of_birth = ""

    try:
        signatories_list = list()
        director_list = list()
        created_bvn = list()
        business = account_request.creation_request
        signatories = json.loads(business.signatories)
        directors = json.loads(business.directors)
        first_director = True
        for director in directors:

            first_name = str(director.get("first_name")) + " " + str(director.get("other_name"))
            last_name = director.get("last_name")
            email = director.get("email")
            address = director.get("address")
            if director.get("gender") == "male":
                gender = "0"
            else:
                gender = "1"
            date_of_birth = str(director.get("date_of_birth"))
            phone_number = director.get("phone_number")
            state_of_origin = director.get("state_of_origin")
            nin_number = director.get("nin_number")
            bvn_number = director.get("bvn_number")
            if first_director:
                company_other_name = first_name
                company_last_name = last_name
                # company_bvn = bvn_number
                company_gender = gender
                company_place_of_birth = state_of_origin
                company_date_of_birth = date_of_birth
                first_director = False

            if bvn_number not in created_bvn:
                response = client.create_customer(
                    last_name=last_name, other_names=first_name, address=address, gender=gender, date_of_birth=date_of_birth, phone_no=phone_number,
                    place_of_birth=state_of_origin, national_identity_no=nin_number, bvn=bvn_number, account_officer_code=account_officer_code,
                    email=email
                )
                if "CustomerID" in response:
                    director_list.append(response.get("CustomerID"))
                    created_bvn.append(response.get("BankVerificationNumber"))

            for mandate in signatories:
                """
                      "first_name": "Test",
                      "last_name": "Last",
                      "other_name": "Other",
                      "email": "<EMAIL>",
                      "address": "Test Address",
                      "gender": "male",
                      "date_of_birth": "2000-05-31",
                      "phone_number": "***********",
                      "state_of_origin": "Lagos",
                      "nin_number": "*************",
                      "bvn_number": "***********"
                """
                first_name = str(mandate.get("first_name")) + " " + str(mandate.get("other_name"))
                last_name = mandate.get("last_name")
                email = mandate.get("email")
                address = mandate.get("address")
                if mandate.get("gender") == "male":
                    gender = 0
                else:
                    gender = 1
                date_of_birth = str(mandate.get("date_of_birth"))
                phone_number = mandate.get("phone_number")
                state_of_origin = mandate.get("state_of_origin")
                nin_number = mandate.get("nin_number")
                bvn_number = mandate.get("bvn_number")

                if bvn_number not in created_bvn:
                    response = client.create_customer(
                        last_name=last_name, other_names=first_name, address=address, gender=gender, date_of_birth=date_of_birth,
                        phone_no=phone_number,
                        place_of_birth=state_of_origin, national_identity_no=nin_number, bvn=bvn_number, account_officer_code=account_officer_code,
                        email=email
                    )
                    if "CustomerID" in response:
                        signatories_list.append(response.get("CustomerID"))
                        created_bvn.append(response.get("BankVerificationNumber"))

        # Create Corporate Customer
        email = business.business_email
        name = business.business_name
        phone_number = business.business_phone_number
        address = business.business_address
        website = business.business_website
        sector = business.business_sector
        contact_person_name = business.contact_person_name
        contact_person_phone = business.contact_person_phone
        tax_number = business.business_tax_number
        registration_number = business.business_registration_number
        company_bvn = str(created_bvn[0])
        registration_date = str(business.business_registration_date)
        corporate_response = client.create_organization_customer(
            name=name, phone_no=contact_person_phone, postal_address=address, business_phone_no=phone_number, tax_id_no=tax_number,
            business_name=name, trade_name=name, industrial_sector=sector, email=email, address=address, company_reg_date=registration_date,
            contact_person_name=contact_person_name, web_address=website, business_commencement_date=registration_date,
            registration_number=registration_number, customer_members=signatories_list, the_directors=director_list
        )
        business.account_creation_response = corporate_response
        business.save()
        if "IsSuccessful" in corporate_response and corporate_response.get("IsSuccessful") is True:
            customer_id = corporate_response.get("CustomerIDInString")
            # Create Company
            company = Company.objects.create(
                name=name, bank_customer_id=str(director_list[0]), active=True, address=address, email=email, phone_number=phone_number,
                website=website, tax_number=tax_number, registration_number=registration_number, registration_date=registration_date,
                corporate_customer_id=customer_id, company_bvn=company_bvn
            )
            # Create Account for Company
            account_creation_response = client.create_account_quick(
                transaction_tracking_ref=generate_transaction_reference(), account_opening_tracking_ref=generate_transaction_reference(),
                customer_id=str(director_list[0]), last_name=company_last_name, other_names=company_other_name, account_name=str(name).upper(),
                phone_no=phone_number, gender=company_gender, place_of_birth=company_place_of_birth, date_of_birth=company_date_of_birth,
                address=address, account_officer_code=account_officer_code, email=email, bvn=company_bvn
            )
            """
            {
                "IsSuccessful": true,
                "CustomerIDInString": null,
                "Message": {
                    "AccountNumber": "**********",
                    "BankoneAccountNumber": "*****************",
                    "CustomerID": "022978",
                    "FullName": "Tester Quick",
                    "CreationMessage": null,
                    "Id": 506495
                },
                "TransactionTrackingRef": null,
                "Page": null
            }
            """
            if "IsSuccessful" in account_creation_response and account_creation_response.get("IsSuccessful") is True \
                    and account_creation_response.get("Message"):
                response_message = account_creation_response.get("Message")
                account_no = response_message.get("AccountNumber")
                bank_one_account_no = response_message.get("BankoneAccountNumber")
                CompanyAccount.objects.create(
                    company=company, account_number=account_no, bank_one_account_number=bank_one_account_no, account_officer=account_officer
                )
            admin_user = AdminUser.objects.get(id=user_id)
            account_request.approved_at = timezone.now()
            account_request.approved_by = admin_user
            account_request.save()

        else:
            # Set Creation request back to pending
            account_request.status = "pending"
            account_request.approved_at = None
            account_request.approved_by = None
            account_request.save()
    except Exception as err:
        # Set Creation request back to pending
        account_request.status = "pending"
        account_request.approved_at = None
        account_request.approved_by = None
        account_request.creation_request.account_creation_response = err
        account_request.creation_request.save()
        account_request.save()

    return True


@shared_task()
def send_notification_task(
    notification_type: str,
    recipient: str,
    message: str,
    subject: str = None,
    account_number: str = None,
    reference_no: str = None,
    institution_code: str = None,
    mfb_code: str = None,
    email_from: str = None
):
    """
    Shared task for sending SMS and email notifications.

    This task uses tmsaas functions if DEBUG is True, otherwise uses BankOne API.

    Args:
        notification_type: 'sms' or 'email'
        recipient: Phone number for SMS or email address for email
        message: Message content
        subject: Email subject (required for email)
        account_number: Account number for SMS (required for SMS via BankOne)
        reference_no: Reference number for SMS (required for SMS via BankOne)
        institution_code: Institution code for email (required for email via BankOne)
        mfb_code: MFB code for email (required for email via BankOne)
        email_from: From email address (required for email via BankOne)

    Returns:
        dict: Response from the notification service
    """
    from django.conf import settings
    from bowenmfb.modules.utils import send_tmsaas_sms, send_tmsaas_email, log_request
    from bowenmfb.modules.bankone import BankOneClient

    try:
        if settings.DEBUG:
            # Use tmsaas functions in DEBUG mode
            if notification_type == 'sms':
                log_request(f"Sending SMS via tmsaas to {recipient}: {message}")
                response = send_tmsaas_sms(content=message, receiver=recipient)
                log_request(f"tmsaas SMS response: {response}")
                return {
                    'success': True,
                    'service': 'tmsaas',
                    'type': 'sms',
                    'recipient': recipient,
                    'response': response
                }
            elif notification_type == 'email':
                if not subject:
                    raise ValueError("Subject is required for email notifications")
                log_request(f"Sending email via tmsaas to {recipient}: {subject}")
                response = send_tmsaas_email(content=message, receiver=recipient, subject=subject)
                log_request(f"tmsaas email response: {response}")
                return {
                    'success': True,
                    'service': 'tmsaas',
                    'type': 'email',
                    'recipient': recipient,
                    'response': response
                }
            else:
                raise ValueError(f"Unsupported notification type: {notification_type}")
        else:
            # Use BankOne API in production mode
            client = BankOneClient()

            if notification_type == 'sms':
                if not account_number or not reference_no:
                    raise ValueError("account_number and reference_no are required for SMS via BankOne")

                sms_messages = [{
                    "AccountNumber": account_number,
                    "To": recipient,
                    "Body": message,
                    "ReferenceNo": reference_no
                }]

                log_request(f"Sending SMS via BankOne to {recipient}: {message}")
                response = client.send_bulk_sms(sms_messages)
                log_request(f"BankOne SMS response: {response}")

                return {
                    'success': response.get('Status', False),
                    'service': 'bankone',
                    'type': 'sms',
                    'recipient': recipient,
                    'response': response
                }
            elif notification_type == 'email':
                if not all([subject, institution_code, mfb_code, email_from]):
                    raise ValueError("subject, institution_code, mfb_code, and email_from are required for email via BankOne")

                email_messages = [{
                    "InstitutionCode": institution_code,
                    "MfbCode": mfb_code,
                    "emailFrom": email_from,
                    "emailTo": recipient,
                    "Subject": subject,
                    "Message": message
                }]

                log_request(f"Sending email via BankOne to {recipient}: {subject}")
                response = client.send_email(email_messages)
                log_request(f"BankOne email response: {response}")

                return {
                    'success': response.get('Status', False),
                    'service': 'bankone',
                    'type': 'email',
                    'recipient': recipient,
                    'response': response
                }
            else:
                raise ValueError(f"Unsupported notification type: {notification_type}")

    except Exception as e:
        log_request(f"Error sending {notification_type} notification to {recipient}: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'type': notification_type,
            'recipient': recipient
        }



