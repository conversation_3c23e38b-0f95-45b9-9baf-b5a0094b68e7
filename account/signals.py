from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.db import transaction

from account.models import Customer, CompanyAccount
from bowenmfb.modules.utils import create_in_app_notification, send_notification_async, log_request


@receiver(post_save, sender=Customer)
def notify_new_user_signup(sender, instance, created, **kwargs):
    """Create in-app notification when a new user signs up in a company."""
    if created and instance.company:
        transaction.on_commit(lambda: create_in_app_notification(
            company=instance.company,
            title="New User Signup",
            message=f"A new user {instance.user.get_full_name()} has signed up for your company.",
            notification_type='user_signup',
            related_object_id=instance.id,
            related_object_type='Customer'
        ))


@receiver(pre_save, sender=CompanyAccount)
def track_transfer_limit_changes(sender, instance, **kwargs):
    """Track transfer limit changes before saving."""
    if instance.pk:
        try:
            old_instance = CompanyAccount.objects.get(pk=instance.pk)
            instance._old_daily_limit = old_instance.daily_transfer_limit
            instance._old_monthly_limit = old_instance.monthly_transfer_limit
        except CompanyAccount.DoesNotExist:
            instance._old_daily_limit = None
            instance._old_monthly_limit = None
    else:
        instance._old_daily_limit = None
        instance._old_monthly_limit = None


@receiver(post_save, sender=CompanyAccount)
def notify_transfer_limit_update(sender, instance, created, **kwargs):
    """Create in-app notification when transfer limits are updated."""
    if not created and instance.company:
        # Check if limits have changed
        daily_changed = (hasattr(instance, '_old_daily_limit') and
                        instance._old_daily_limit is not None and
                        instance._old_daily_limit != instance.daily_transfer_limit)

        monthly_changed = (hasattr(instance, '_old_monthly_limit') and
                          instance._old_monthly_limit is not None and
                          instance._old_monthly_limit != instance.monthly_transfer_limit)

        if daily_changed or monthly_changed:
            changes = []
            if daily_changed:
                changes.append(f"Daily limit: ₦{instance._old_daily_limit:,.2f} → ₦{instance.daily_transfer_limit:,.2f}")
            if monthly_changed:
                changes.append(f"Monthly limit: ₦{instance._old_monthly_limit:,.2f} → ₦{instance.monthly_transfer_limit:,.2f}")

            message = f"Transfer limits have been updated for your account. Changes: {'; '.join(changes)}"

            transaction.on_commit(lambda: create_in_app_notification(
                company=instance.company,
                title="Transfer Limits Updated",
                message=message,
                notification_type='transfer_limit_updated',
                related_object_id=instance.id,
                related_object_type='CompanyAccount'
            ))


@receiver(post_save, sender=CompanyAccount)
def notify_new_account_created(sender, instance, created, **kwargs):
    """Create in-app notification when a new account is created for a company."""
    if created and instance.company:
        transaction.on_commit(lambda: create_in_app_notification(
            company=instance.company,
            title="New Account Created",
            message=f"A new account has been created for your company: {instance.account_number}",
            notification_type='account_created',
            related_object_id=instance.id,
            related_object_type='CompanyAccount'
        ))

        # Send email and sms to all company customers and business email
        try:
            company_customers = Customer.objects.filter(company=instance.company, active=True)
            for customer in company_customers:
                # Send SMS notification and bank_customer_id
                sms_message = (f"New account created for {instance.company.name}. Account Number: {instance.account_number}, "
                               f"Bank Customer ID: {instance.company.bank_customer_id}. You can now use this account for transactions.")

                send_notification_async(
                    notification_type='sms',
                    recipient=customer.phone_number,
                    message=sms_message
                )

                # Send email notification with HTML formatting
                email_message = f"""
                <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h2 style="color: #28a745; margin-bottom: 20px;">🎉 New Account Created</h2>

                            <p>Dear <strong>{customer.user.first_name}</strong>,</p>

                            <p>Great news! A new account has been successfully created for your company: <strong>{instance.company.name}</strong></p>

                            <div style="background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                                <h3 style="color: #2c5aa0; margin-bottom: 15px;">Account Details:</h3>
                                <ul style="list-style: none; padding: 0; margin: 0;">
                                    <li style="margin-bottom: 8px;"><strong>Account Number:</strong> {instance.account_number}</li>
                                    <li style="margin-bottom: 8px;"><strong>Bank One Account Number:</strong> {instance.bank_one_account_number}</li>
                                    <li style="margin-bottom: 8px;"><strong>Account Officer:</strong> {instance.account_officer.name}</li>
                                    <li style="margin-bottom: 8px;"><strong>Customer ID:</strong> {instance.company.bank_customer_id}</li>
                                </ul>
                            </div>

                            <p>You can now use this account for your business transactions through our platform.</p>

                            <p>If you have any questions or need assistance, please don't hesitate to contact your account officer.</p>

                            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                            <p style="color: #666; font-size: 14px;">
                                Best regards,<br>
                                <strong>Bowen MFB Team</strong>
                            </p>
                        </div>
                    </div>
                </body>
                </html>
                """

                send_notification_async(
                    notification_type='email',
                    recipient=customer.user.email,
                    message=email_message,
                    subject=f'New Account Created - {instance.company.name}'
                )

            # Send email to business email with HTML formatting
            email_message = f"""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                        <h2 style="color: #28a745; margin-bottom: 20px;">🎉 New Account Created</h2>

                        <p>Dear <strong>{instance.company.name}</strong>,</p>

                        <p>Great news! A new account has been successfully created for your company.</p>

                        <div style="background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <h3 style="color: #2c5aa0; margin-bottom: 15px;">Account Details:</h3>
                            <ul style="list-style: none; padding: 0; margin: 0;">
                                <li style="margin-bottom: 8px;"><strong>Account Number:</strong> {instance.account_number}</li>
                                <li style="margin-bottom: 8px;"><strong>Bank One Account Number:</strong> {instance.bank_one_account_number}</li>
                                <li style="margin-bottom: 8px;"><strong>Account Officer:</strong> {instance.account_officer.name}</li>
                                <li style="margin-bottom: 8px;"><strong>Customer ID:</strong> {instance.company.bank_customer_id}</li>
                            </ul>
                        </div>

                        <p>You can now use this account for your business transactions through our platform.</p>

                        <p>If you have any questions or need assistance, please don't hesitate to contact your account officer.</p>

                        <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                        <p style="color: #666; font-size: 14px;">
                            Best regards,<br>
                            <strong>Bowen MFB Team</strong>
                        </p>
                    </div>
                </div>
            </body>
            </html>
            """

            send_notification_async(
                notification_type='email',
                recipient=instance.company.email,
                message=email_message,
                subject=f'New Account Created - {instance.company.name}'
            )

        except Exception as e:
            log_request(f"Failed to send new account notifications for company {instance.company.name}: {str(e)}")
