# Generated by Django 5.2.1 on 2025-07-21 21:30

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0015_inappnotification'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='customer',
            name='admin_approved',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='customer',
            name='admin_approved_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='customer',
            name='admin_approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customer_approved_by', to=settings.AUTH_USER_MODEL),
        ),
    ]
