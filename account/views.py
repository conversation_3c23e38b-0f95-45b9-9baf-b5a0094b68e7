from drf_spectacular.utils import extend_schema, OpenApiParameter
from rest_framework import status
from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from bowenmfb.modules.paginations import CustomPagination
from transfer.models import BankList
from transfer.serializers import BankListSerializerOut
from .serializers import *
from bowenmfb.modules.exceptions import raise_serializer_error_msg, BankOneAPIError


class AccountCreationAPIView(APIView):
    permission_classes = []

    @extend_schema(
        parameters=[
            OpenApiParameter(name="business_email", type=str, required=True, description="Request business email"),
        ],
        responses={status.HTTP_200_OK: CompanyCreationRequestSerializerOut}
    )
    def get(self, request):
        business_email = request.GET.get("business_email")
        try:
            creation_request = CompanyCreationRequest.objects.get(business_email__iexact=business_email)
            serializer = CompanyCreationRequestSerializerOut(creation_request)
            return Response(serializer.data)
        except CompanyCreationRequest.DoesNotExist:
            return Response({"detail": "Request not found"})

    @extend_schema(request=CompanyCreationRequestIn, responses={status.HTTP_200_OK})
    def post(self, request):
        method = request.method
        serializer = CompanyCreationRequestIn(data=request.data, context={"method": method})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class AccountCreationRequestUpdateAPIView(APIView):
    permission_classes = []

    @extend_schema(request=CompanyCreationRequestIn, responses={status.HTTP_200_OK})
    def put(self, request, request_id):
        method = request.method
        try:
            instance = CompanyCreationRequest.objects.get(id=request_id)
        except CompanyCreationRequest.DoesNotExist:
            raise InvalidRequestException({"detail": "Request not found"})

        serializer = CompanyCreationRequestIn(data=request.data, context={"method": method}, instance=instance)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class SignUpAPIView(APIView):
    permission_classes = []

    @extend_schema(request=SignUpSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = SignUpSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class LoginAPIView(APIView):
    permission_classes = []

    @extend_schema(request=LoginSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = LoginSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class EmailVerificationAPIView(APIView):
    permission_classes = []

    @extend_schema(request=EmailVerificationSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = EmailVerificationSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class RequestVerificationCodeAPIView(APIView):
    permission_classes = []

    @extend_schema(request=RequestVerificationLinkSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = RequestVerificationLinkSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class ChangePasswordAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=ChangePasswordSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = ChangePasswordSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class ResetPasswordAPIView(APIView):
    permission_classes = []

    @extend_schema(request=ResetPasswordSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = ResetPasswordSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class BankConstantUpdateAPIView(APIView):
    permission_classes = []  # This is temporal
    
    @extend_schema(request=BankConstantTableSerializerIn, responses={status.HTTP_200_OK: BankConstantTableSerializerOut})
    def post(self, request):
        serializer = BankConstantTableSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class FetchAccountOfficersAPIView(APIView):
    permission_classes = []  # Consider adding proper permissions in production

    @extend_schema(responses={status.HTTP_200_OK: AccountOfficerSerializerOut(many=True)})
    def get(self, request):
        try:
            client = BankOneClient()
            response = client.get_account_officers()
            created_count = 0
            updated_count = 0

            # response = [{"Name":"DUREKE, PAUL","Code":"003","Branch":None,"Gender":None,"PhoneNumber":None,"Email":None,"Id":None}]

            for officer_data in response:
                email_address = officer_data.get("Email")
                phone_number = officer_data.get("PhoneNumber")
                name = str(officer_data.get("Name")).replace(",", "").replace(" ", "").lower()
                officer, created = AccountOfficer.objects.update_or_create(
                    code=officer_data.get("Code"),
                    defaults={
                        "name": officer_data.get("Name"),
                        "email": email_address if email_address else f"{name}@bowenmfb.com",
                        "gender": officer_data.get("Gender"),
                        "phone_number": phone_number if phone_number else str("080") + str(uuid.uuid4().int)[:8]
                    }
                )

                if created:
                    created_count += 1
                else:
                    updated_count += 1

            # Return all account officers
            officers = AccountOfficer.objects.all()
            serializer = AccountOfficerSerializerOut(officers, many=True)

            return Response({
                "message": f"Successfully processed account officers. Created: {created_count}, Updated: {updated_count}",
                "data": serializer.data
            })

        except BankOneAPIError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UpdateBankListAPIView(APIView):
    permission_classes = []  # Consider adding proper permissions in production

    @extend_schema(responses={status.HTTP_200_OK: AccountOfficerSerializerOut(many=True)})
    def post(self, request):
        try:
            client = BankOneClient()
            response = client.get_commercial_banks()
            created_count = 0
            updated_count = 0
            """
            [
                {
                    "Code": "076",
                    "Gateway": null,
                    "ID": "1",
                    "Name": "Skye Bank",
                    "Status": false,
                    "StatusDetails": null,
                    "RequestStatus": false,
                    "ResponseDescription": null,
                    "ResponseStatus": null
                },
                {
                    "Code": "035",
                    "Gateway": null,
                    "ID": "2",
                    "Name": "Wema Bank",
                    "Status": false,
                    "StatusDetails": null,
                    "RequestStatus": false,
                    "ResponseDescription": null,
                    "ResponseStatus": null
                },
            ]
            """

            for bank in response:
                officer, created = BankList.objects.update_or_create(
                    code=bank.get("Code"),
                    defaults={
                        "name": bank.get("Name"),
                    }
                )

                if created:
                    created_count += 1
                else:
                    updated_count += 1

            # Return all account officers
            bank_list = BankList.objects.all()
            serializer = BankListSerializerOut(bank_list, many=True)

            return Response({
                "message": f"Successfully updated bank lists. Created: {created_count}, Updated: {updated_count}",
                "data": serializer.data
            })

        except BankOneAPIError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CreateAdditionalAccountAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=CreateAdditionalAccountSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = CreateAdditionalAccountSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class TransactionPinChangeAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=ChangeTransactionPinSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = ChangeTransactionPinSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class ListOtherCompanyCustomersAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated]

    @extend_schema(responses={status.HTTP_200_OK: CustomerSerializerOut(many=True)})
    def get(self, request):
        user = request.user
        company = user.customer.company
        customers = Customer.objects.filter(company=company).exclude(user=user).order_by('-created_at')
        queryset = self.paginate_queryset(customers, request)
        serializer = CustomerSerializerOut(queryset, many=True)
        return Response({
            "detail": "Company customers retrieved successfully",
            "data": self.get_paginated_response(serializer.data).data
        })


class UpdateProfileImageAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=UpdateProfileImageSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = UpdateProfileImageSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class CompanyInAppNotificationListAPIView(ListAPIView):
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination
    serializer_class = InAppNotificationSerializerOut

    def get_queryset(self):
        notification_type = self.request.GET.get("notification_type", None)
        is_read = self.request.GET.get("is_read", None)
        query = Q(company=self.request.user.customer.company)
        if notification_type:
            query &= Q(notification_type=notification_type)
        if is_read == "true":
            query &= Q(is_read=True)
        return InAppNotification.objects.filter(query).order_by("-created_at")



class ExistingCustomerAccountAPIView(APIView):
    permission_classes = []

    @extend_schema(request=ExistingCustomerAccountSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = ExistingCustomerAccountSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


