from django.urls import path
from . import views


app_name = "account"

urlpatterns = [
    path('create-account', views.AccountCreationAPIView.as_view(), name="create-account"),
    path('create-account/<uuid:request_id>/edit', views.AccountCreationRequestUpdateAPIView.as_view(), name="create-account=update"),
    path('register', views.SignUpAPIView.as_view(), name="register"),
    path('login', views.LoginAPIView.as_view(), name="login"),
    path('verify-email', views.EmailVerificationAPIView.as_view(), name="verify-email"),
    path('request-verification-code', views.RequestVerificationCodeAPIView.as_view(), name="request-verification-code"),
    path('change-password', views.ChangePasswordAPIView.as_view(), name="change-password"),
    path('reset-password', views.ResetPasswordAPIView.as_view(), name="reset-password"),
    path('create-additional-account', views.CreateAdditionalAccountAPIView.as_view(), name="create-additional-account"),
    path('change-transaction-pin', views.TransactionPinChangeAPIView.as_view(), name="change-transaction-pin"),
    path('reset-transaction-pin', views.TransactionPinResetAPIView.as_view(), name="reset-transaction-pin"),
    path('list-other-company-customers', views.ListOtherCompanyCustomersAPIView.as_view(), name="list-other-company-customers"),
    path('update-profile-image', views.UpdateProfileImageAPIView.as_view(), name="update-profile-image"),
    path('notifications', views.CompanyInAppNotificationListAPIView.as_view(), name="in-app-notifications"),
    path('existing-customer-account', views.ExistingCustomerAccountAPIView.as_view(), name="existing-customer-account"),
]

