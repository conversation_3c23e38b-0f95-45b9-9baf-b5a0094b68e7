import base64
import json
import re
from datetime import timedelta

from django.conf import settings
from django.contrib.auth import authenticate
from django.contrib.auth.hashers import make_password, check_password
from django.contrib.auth.password_validation import validate_password
from django.db.models import Q
from django.utils import timezone
from rest_framework import serializers
from rest_framework_simplejwt.tokens import AccessToken

from bowenmfb.modules.bankone import BankOneClient
from bowenmfb.modules.exceptions import InvalidRequestException, MaximumApprovalPINRetriesException
from bowenmfb.modules.utils import format_phone_number, encrypt_text, generate_random_otp, log_request, get_next_minute, \
    validate_bvn_with_phone_number, generate_transaction_reference, send_notification_async, validate_customer_with_info_on_bankone
from superadmin.models import AccountCreationRequest
from transfer.models import AccountSignatory
from transfer.serializers import AccountSignatorySerializerOut
from .models import *

client = BankOneClient()


class BankConstantTableSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = BankConstantTable
        exclude = ["auth_token"]


class AccountOfficerSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = AccountOfficer
        exclude = []


class CompanyAccountSerializerOut(serializers.ModelSerializer):
    account_officer = serializers.SerializerMethodField()
    account_number = serializers.CharField(source="get_masked_account_number")

    def get_account_officer(self, obj):
        return AccountOfficerSerializerOut(obj.account_officer).data if obj.account_officer else None

    class Meta:
        model = CompanyAccount
        exclude = ["company", "bank_one_account_number"]


class CompanySerializerOut(serializers.ModelSerializer):
    tax_number = serializers.CharField(source="get_masked_tax_number")
    registration_number = serializers.CharField(source="get_registration_number")
    company_bvn = serializers.CharField(source="get_company_bvn")
    accounts = serializers.SerializerMethodField()

    def get_accounts(self, obj):
        return CompanyAccountSerializerOut(CompanyAccount.objects.filter(company=obj), many=True).data

    class Meta:
        model = Company
        exclude = ["corporate_customer_id"]


class CustomerSerializerOut(serializers.ModelSerializer):
    first_name = serializers.CharField(source="user.first_name")
    last_name = serializers.CharField(source="user.last_name")
    username = serializers.CharField(source="user.username")
    email = serializers.CharField(source="user.email")
    date_joined = serializers.CharField(source="user.date_joined")
    bvn_number = serializers.CharField(source="get_masked_bvn")
    nin_number = serializers.CharField(source="get_masked_nin")
    company = serializers.SerializerMethodField()
    signatory_access = serializers.SerializerMethodField()

    def get_signatory_access(self, obj):
        return AccountSignatorySerializerOut(AccountSignatory.objects.filter(customer=obj), many=True).data

    def get_company(self, obj):
        return CompanySerializerOut(obj.company).data if obj.company else None

    class Meta:
        model = Customer
        exclude = ["approval_pin", "verification_token", "verification_token_expiry"]


class CompanyCreationRequestSerializerOut(serializers.ModelSerializer):
    signatories = serializers.SerializerMethodField()
    directors = serializers.SerializerMethodField()

    def get_signatories(self, obj):
        return json.loads(obj.signatories) if obj.signatories else ""

    def get_directors(self, obj):
        return json.loads(obj.directors) if obj.directors else ""

    class Meta:
        model = CompanyCreationRequest
        exclude = []


# class UserSerializerOut

class CustomerDetailSerializerIn(serializers.Serializer):
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    other_name = serializers.CharField(required=False)
    email = serializers.EmailField()
    address = serializers.CharField()
    gender = serializers.ChoiceField(choices=GENDER_TYPE_CHOICES)
    date_of_birth = serializers.CharField()
    phone_number = serializers.CharField()
    state_of_origin = serializers.CharField()
    nin_number = serializers.IntegerField()
    bvn_number = serializers.IntegerField()


class CompanyCreationRequestIn(serializers.Serializer):
    # BUSINESS DATA
    # cac_doc, board_resolution, reference_form
    business_name = serializers.CharField()
    business_email = serializers.EmailField()
    business_phone_number = serializers.IntegerField()
    business_address = serializers.CharField()
    # business_bvn = serializers.CharField()
    business_website = serializers.CharField(required=False)
    business_sector = serializers.CharField(required=False)
    business_tax_number = serializers.CharField(required=False)
    business_registration_number = serializers.CharField()
    business_registration_date = serializers.CharField()
    contact_person_name = serializers.CharField()
    contact_person_phone = serializers.IntegerField()
    cac_document = serializers.CharField()
    board_resolution = serializers.CharField()
    reference_form = serializers.CharField()
    signatories = serializers.ListSerializer(child=CustomerDetailSerializerIn())
    directors = serializers.ListSerializer(child=CustomerDetailSerializerIn())

    def validate(self, attrs):
        name = attrs.get("business_name")
        signatories = attrs.get("signatories")
        directors = attrs.get("directors")
        if Company.objects.filter(name__iexact=name).exists():
            raise InvalidRequestException({"detail": "Business name taken"})

        method = self.context.get("method")
        if method == "POST" and CompanyCreationRequest.objects.filter(business_name__iexact=name).exists():
            raise InvalidRequestException({"detail": "Request with same business name already exist"})

        if method == "PUT":
            business_email = attrs.get("business_email")
            try:
                company_request = CompanyCreationRequest.objects.get(business_email__iexact=business_email)
            except CompanyCreationRequest.DoesNotExist:
                raise InvalidRequestException({"detail": "Request not found"})

            if AccountCreationRequest.objects.filter(creation_request=company_request, status='success').exists():
                raise InvalidRequestException({"detail": "Request has been approved and cannot be updated"})

        signatories_data = list()

        for director in directors:
            director_bvn = str(director.get("bvn_number")).strip()
            phone_number = str(director.get("phone_number")).strip()
            email = director.get("email")

            # Validate BVN, phone and email are not already registered in BankOne
            result = validate_customer_with_info_on_bankone(director_bvn, email, phone_number)
            if not result["can_proceed"]:
                raise InvalidRequestException({"detail": "BVN, phone number or email already registered"})

            if not (str(director_bvn).isnumeric() or len(director_bvn) == 11):
                raise InvalidRequestException({"detail": "Director's BVN is not valid"})

            bvn_validation = validate_bvn_with_phone_number(director_bvn, director.get("phone_number"))
            if not bvn_validation:
                raise InvalidRequestException({"detail": f"BVN: {director_bvn} is not valid or BVN registered phone number mismatch"})

            signatories_data.append(email)
            signatories_data.append(director_bvn)
            signatories_data.append(director.get("nin_number"))

        for director in signatories:
            director_bvn = str(director.get("bvn_number")).strip()
            phone_number = str(director.get("phone_number")).strip()
            email = director.get("email")

            # Validate BVN, phone and email are not already registered in BankOne
            result = validate_customer_with_info_on_bankone(director_bvn, email, phone_number)
            if not result["can_proceed"]:
                raise InvalidRequestException({"detail": "BVN, phone number or email already registered"})

            if not (str(director_bvn).isnumeric() or len(director_bvn) == 11):
                raise InvalidRequestException({"detail": "Signatory's BVN is not valid"})

            signatories_data.append(email)
            signatories_data.append(director_bvn)
            signatories_data.append(director.get("nin_number"))

            bvn_validation = validate_bvn_with_phone_number(director_bvn, director.get("phone_number"))
            if not bvn_validation:
                raise InvalidRequestException({"detail": f"BVN: {director_bvn} is not valid or BVN registered phone number mismatch"})

        return attrs

    def create(self, validated_data):
        business_name = validated_data.get("business_name")
        business_phone_number = validated_data.get("business_phone_number")
        business_email = validated_data.get("business_email")
        business_address = validated_data.get("business_address")
        business_website = validated_data.get("business_website")
        # business_bvn = validated_data.get("business_bvn")
        business_sector = validated_data.get("business_sector")
        business_tax_number = validated_data.get("business_tax_number")
        business_registration_number = validated_data.get("business_registration_number")
        business_registration_date = validated_data.get("business_registration_date")
        contact_person_name = validated_data.get("contact_person_name")
        contact_person_phone = validated_data.get("contact_person_phone")
        signatories = validated_data.get("signatories")
        directors = validated_data.get("directors")
        cac_document = validated_data.get("cac_document")
        board_resolution = validated_data.get("board_resolution")
        reference_form = validated_data.get("reference_form")

        if signatories:
            signatories = json.dumps(signatories)

        create_request = CompanyCreationRequest.objects.create(
            business_email=business_email, business_name=business_name, business_phone_number=business_phone_number,
            business_address=business_address, business_website=business_website, business_sector=business_sector,
            contact_person_name=contact_person_name, contact_person_phone=contact_person_phone, business_tax_number=business_tax_number,
            business_registration_number=business_registration_number, business_registration_date=business_registration_date, signatories=signatories,
            directors=json.dumps(directors), cac_document=cac_document, board_resolution=board_resolution, reference_form=reference_form
        )

        AccountCreationRequest.objects.create(creation_request=create_request)

        return {"detail": "Account creation request submitted successfully. Your request will be reviewed shortly"}

    def update(self, instance, validated_data):
        instance.business_email = validated_data.get("business_email", instance.business_email)
        instance.business_phone_number = validated_data.get("business_phone_number", instance.business_phone_number)
        instance.business_address = validated_data.get("business_address", instance.business_address)
        instance.business_website = validated_data.get("business_website", instance.business_website)
        instance.business_sector = validated_data.get("business_sector", instance.business_sector)
        instance.business_tax_number = validated_data.get("business_tax_number", instance.business_tax_number)
        instance.business_registration_number = validated_data.get("business_registration_number", instance.business_registration_number)
        instance.business_registration_date = validated_data.get("business_registration_date", instance.business_registration_date)
        instance.contact_person_name = validated_data.get("contact_person_name", instance.contact_person_name)
        instance.contact_person_phone = validated_data.get("contact_person_phone", instance.contact_person_phone)
        instance.cac_document = validated_data.get("cac_document", instance.cac_document)
        instance.board_resolution = validated_data.get("board_resolution", instance.board_resolution)
        instance.reference_form = validated_data.get("reference_form", instance.reference_form)
        instance.signatories = validated_data.get("signatories", instance.signatories)
        instance.directors = validated_data.get("directors", instance.directors)
        instance.business_name = validated_data.get("business_name", instance.business_name)
        instance.save()
        return {"detail": "Account creation request updated successfully. Your request will be reviewed shortly"}


class SignUpSerializerIn(CustomerDetailSerializerIn):
    company_id = serializers.CharField()
    transaction_pin = serializers.CharField(max_length=4)
    profile_picture = serializers.CharField(required=False)
    username = serializers.CharField()
    password = serializers.CharField()
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    other_name = serializers.CharField()
    email = serializers.EmailField()
    address = serializers.CharField()
    gender = serializers.ChoiceField(choices=GENDER_TYPE_CHOICES)
    date_of_birth = serializers.DateTimeField()
    phone_number = serializers.CharField()
    state_of_origin = serializers.CharField()
    nin_number = serializers.CharField()
    bvn_number = serializers.CharField()

    def validate(self, attrs):
        username = attrs.get("username")
        email = attrs.get("email")
        transaction_pin = attrs.get("transaction_pin")
        company_id = attrs.get("company_id")
        bvn_number = attrs.get("bvn_number")
        phone_number = attrs.get("phone_number")

        bvn_validation = validate_bvn_with_phone_number(bvn_number, phone_number)
        if not bvn_validation:
            raise InvalidRequestException({"detail": f"BVN: {bvn_number} is not valid or BVN registered phone number mismatch"})

        try:
            Company.objects.get(bank_customer_id=company_id)
        except Company.DoesNotExist:
            raise InvalidRequestException({"detail": "Company not found"})

        query = Q(email__iexact=email) | Q(username__iexact=username)

        if User.objects.filter(query).exists():
            raise InvalidRequestException({"detail": "Username or email already taken"})
        if not (str(transaction_pin).isnumeric() or len(transaction_pin) == 4):
            raise InvalidRequestException({"detail": "Transaction PIN must be four (4) digits"})

        return attrs

    def validate_password(self, password):
        user = User(
            username=self.initial_data.get("username"),
            email=self.initial_data.get("email")
        )
        validate_password(password, user)
        return password

    def create(self, validated_data):
        first_name = validated_data.get("first_name")
        last_name = validated_data.get("last_name")
        other_name = validated_data.get("other_name")
        email = validated_data.get("email")
        address = validated_data.get("address")
        gender = validated_data.get("gender")
        date_of_birth = validated_data.get("date_of_birth")
        phone_number = validated_data.get("phone_number")
        state_of_origin = validated_data.get("state_of_origin")
        nin_number = validated_data.get("nin_number")
        bvn_number = validated_data.get("bvn_number")
        company_id = validated_data.get("company_id")
        transaction_pin = validated_data.get("transaction_pin")
        profile_picture = validated_data.get("profile_picture")
        username = validated_data.get("username")
        password = validated_data.get("password")

        company = Company.objects.get(bank_customer_id=company_id)

        verification_token = generate_random_otp(6)
        log_request(f"Created account for {email} - {verification_token}")

        user = User.objects.create(email=email, username=username, first_name=first_name, last_name=last_name, password=make_password(password))
        Customer.objects.create(
            user=user, other_name=other_name, address=address, dob=date_of_birth, gender=gender, phone_number=format_phone_number(phone_number),
            state_of_origin=state_of_origin, bvn_number=encrypt_text(bvn_number), nin_number=encrypt_text(nin_number),
            approval_pin=encrypt_text(transaction_pin), image=profile_picture, company=company, verification_token=encrypt_text(verification_token),
            verification_token_expiry=timezone.now() + timedelta(minutes=15)
        )
        # Send welcome SMS and email with verification code
        try:
            welcome_message = f"Welcome to Bowen MFB! Your account has been created successfully. Your verification code is: {verification_token}"

            # Send welcome SMS
            send_notification_async(
                notification_type='sms',
                recipient=phone_number,
                message=welcome_message
            )

            # Send welcome email with verification code
            email_message = f"""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                        <h2 style="color: #2c5aa0; margin-bottom: 20px;">Welcome to Bowen MFB!</h2>

                        <p>Dear <strong>{first_name} {last_name}</strong>,</p>

                        <p>Welcome to Bowen MFB! Your account has been created successfully.</p>

                        <div style="background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <p style="margin: 0;"><strong>Your verification code is:</strong></p>
                            <h3 style="color: #2c5aa0; font-size: 24px; margin: 10px 0; letter-spacing: 2px;">{verification_token}</h3>
                        </div>

                        <p>Please use this code to verify your account. <strong>The code will expire in 15 minutes.</strong></p>

                        <p>Thank you for choosing Bowen MFB.</p>

                        <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                        <p style="color: #666; font-size: 14px;">
                            Best regards,<br>
                            <strong>Bowen MFB Team</strong>
                        </p>
                    </div>
                </div>
            </body>
            </html>
            """

            send_notification_async(
                notification_type='email',
                recipient=email,
                message=email_message,
                subject='Welcome to Bowen MFB - Account Verification'
            )

        except Exception as e:
            log_request(f"Failed to send welcome notifications to {email}: {str(e)}")

        detail = {"detail": "Account created successfully. Please check your email and SMS for verification code"}
        if settings.DEBUG is True:
            detail["verification_code"] = verification_token

        return detail


class LoginSerializerIn(serializers.Serializer):
    username = serializers.CharField()
    password = serializers.CharField()

    def create(self, validated_data):
        username = validated_data.get("username")
        password = validated_data.get("password")

        user = authenticate(username=username, password=password)
        if not user:
            raise InvalidRequestException({"detail": "Invalid email/username or password"})

        try:
            customer = Customer.objects.get(user=user)
        except Customer.DoesNotExist:
            raise InvalidRequestException({"detail": "User not found"})

        if not customer.is_verified:
            new_verification_token = generate_random_otp(6)
            log_request(f"Email Token for email - {user.email}: {new_verification_token}")
            customer.verification_token = encrypt_text(new_verification_token)
            customer.verification_token_expiry = get_next_minute(timezone.datetime.now(), 10)
            customer.save()

            detail = {"detail": "Pending account verification. Please check your email for a new verification code"}
            if settings.DEBUG is True:
                detail["verification_code"] = new_verification_token

            # Send verification token to customer
            raise InvalidRequestException(detail)
        if not customer.active:
            raise InvalidRequestException({"detail": "Your account has been deactivated. Please contact support"})

        if not customer.admin_approved:
            raise InvalidRequestException({"detail": "Your account is pending approval by the bank. Please contact support"})

        access_token = str(AccessToken.for_user(user))
        return {"detail": "Login successful", "data": CustomerSerializerOut(customer).data, "access_token": access_token}


class EmailVerificationSerializerIn(serializers.Serializer):
    email = serializers.EmailField()
    verification_code = serializers.CharField()

    def create(self, validated_data):
        email = validated_data.get("email")
        verification_token = validated_data.get("verification_code")

        customers = Customer.objects.filter(user__email__iexact=email)

        if not customers.exists():
            raise InvalidRequestException({"detail": "User not found"})

        customer = customers.last()

        if customer.is_verified:
            raise InvalidRequestException({"detail": "Account is already verified, please proceed to login"})

        if verification_token != decrypt_text(customer.verification_token):
            raise InvalidRequestException({"detail": "You have provided an invalid verification code"})

        if timezone.now() > customer.verification_token_expiry:
            raise InvalidRequestException({"detail": "Verification code has expired"})

        customer.is_verified = True
        customer.active = True
        customer.verification_token = ""
        customer.save()

        # Send Welcome Email to user
        return {"detail": "Your account is successfully verified, please proceed to login"}


class RequestVerificationLinkSerializerIn(serializers.Serializer):
    email = serializers.EmailField()

    def create(self, validated_data):
        email = validated_data.get("email")
        try:
            customer = Customer.objects.get(user__email=email)
        except Customer.DoesNotExist:
            raise InvalidRequestException({"detail": "User with this email is not found"})

        verification_token = generate_random_otp(6)
        log_request(f"Email Token for email - {email}: {verification_token}")
        customer.verification_token = encrypt_text(verification_token)
        customer.verification_token_expiry = get_next_minute(timezone.datetime.now(), 10)
        customer.save()

        # Send verification code via email and SMS
        try:
            verification_message = f"Your Bowen MFB verification code is: {verification_token}. This code will expire in 15 minutes."

            # Send verification code via SMS
            send_notification_async(
                notification_type='sms',
                recipient=customer.phone_number,
                message=verification_message
            )

            # Send verification code via email
            email_message = f"""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                        <h2 style="color: #2c5aa0; margin-bottom: 20px;">Account Verification Code</h2>

                        <p>Dear <strong>{customer.user.get_full_name()}</strong>,</p>

                        <p>You have requested a new verification code for your Bowen MFB account.</p>

                        <div style="background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <p style="margin: 0;"><strong>Your verification code is:</strong></p>
                            <h3 style="color: #2c5aa0; font-size: 24px; margin: 10px 0; letter-spacing: 2px;">{verification_token}</h3>
                        </div>

                        <p>Please use this code to verify your account. <strong>The code will expire in 15 minutes.</strong></p>

                        <div style="background-color: #fff3cd; padding: 10px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
                            <p style="margin: 0; color: #856404;">
                                <strong>Security Notice:</strong> If you did not request this code, please ignore this message.
                            </p>
                        </div>

                        <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">

                        <p style="color: #666; font-size: 14px;">
                            Best regards,<br>
                            <strong>Bowen MFB Team</strong>
                        </p>
                    </div>
                </div>
            </body>
            </html>
            """

            send_notification_async(
                notification_type='email',
                recipient=email,
                message=email_message,
                subject='Bowen MFB - Account Verification Code'
            )

        except Exception as e:
            log_request(f"Failed to send verification code to {email}: {str(e)}")

        detail = {"detail": "Verification code sent to your email and SMS"}
        if settings.DEBUG is True:
            detail["verification_code"] = verification_token

        return detail


class ChangePasswordSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    old_password = serializers.CharField(required=False)
    new_password = serializers.CharField()
    confirm_password = serializers.CharField(required=False)

    def create(self, validated_data):
        user = validated_data.get("user")
        old_password = validated_data.get("old_password")
        new_password = validated_data.get("new_password")
        confirm_password = validated_data.get("confirm_password")

        if not all([old_password, new_password, confirm_password]):
            raise InvalidRequestException({"detail": "All password fields are required"})

        if not check_password(password=old_password, encoded=user.password):
            raise InvalidRequestException({"detail": "Incorrect old password"})

        if new_password != confirm_password:
            raise InvalidRequestException({"detail": "Passwords mismatch"})

        # Check if new and old passwords are the same
        if old_password == new_password:
            raise InvalidRequestException({"detail": "Same passwords cannot be used"})

        try:
            validate_password(new_password, user)
        except Exception as err:
            raise InvalidRequestException({"detail": f"{err}"})

        user.password = make_password(password=new_password)
        user.save()

        return {"detail": "Password Reset Successful"}


class ResetPasswordSerializerIn(serializers.Serializer):
    verification_code = serializers.CharField()
    password = serializers.CharField()
    email = serializers.EmailField()

    def create(self, validated_data):
        verification_code = validated_data.get("verification_code")
        password = validated_data.get("password")
        email = validated_data.get("email")

        try:
            customer = Customer.objects.get(user__email__iexact=email)
        except Customer.DoesNotExist:
            raise InvalidRequestException({"detail": "User not found"})

        # Check OTP expiry
        if timezone.now() > customer.verification_token_expiry:
            raise InvalidRequestException({"detail": "Verification code has expired"})
        # Compare token
        if verification_code != decrypt_text(customer.verification_token):
            raise InvalidRequestException({"detail": "Verification code is NOT valid"})

        user = customer.user

        try:
            validate_password(password, user)
        except Exception as err:
            raise InvalidRequestException({"detail": f"{err}"})

        user.set_password(raw_password=password)
        user.save()
        return {"detail": "Password reset was successful"}


class BankConstantTableSerializerIn(serializers.Serializer):
    auth_token = serializers.CharField(required=False)
    short_name = serializers.CharField(required=False)
    support_email = serializers.CharField(required=False)
    support_phone = serializers.CharField(required=False)
    website = serializers.CharField(required=False)
    address = serializers.CharField(required=False)
    mfb_code = serializers.CharField(required=False)
    app_version = serializers.CharField(required=False)
    institution_code = serializers.CharField(required=False)
    name = serializers.CharField(required=False)
    dev_base_url = serializers.CharField(required=False)
    prod_base_url = serializers.CharField(required=False)

    def create(self, validated_data):
        auth_token = validated_data.get("auth_token")
        name = validated_data.get("name", "Bowen Micro Finance Bank")

        bank_config, created = BankConstantTable.objects.get_or_create(name=name)

        bank_config.institution_code = validated_data.get("institution_code", bank_config.institution_code)
        bank_config.short_name = validated_data.get("short_name", bank_config.short_name)
        bank_config.support_email = validated_data.get("support_email", bank_config.support_email)
        bank_config.support_phone = validated_data.get("support_phone", bank_config.support_phone)
        bank_config.website = validated_data.get("website", bank_config.website)
        bank_config.address = validated_data.get("address", bank_config.address)
        bank_config.mfb_code = validated_data.get("mfb_code", bank_config.mfb_code)
        bank_config.app_version = validated_data.get("app_version", bank_config.app_version)
        bank_config.dev_base_url = validated_data.get("dev_base_url", bank_config.dev_base_url)
        bank_config.prod_base_url = validated_data.get("prod_base_url", bank_config.prod_base_url)

        if auth_token:
            bank_config.auth_token = encrypt_text(auth_token)

        bank_config.save()

        return BankConstantTableSerializerOut(bank_config, context={"request": self.context.get("request")}).data


class CreateAdditionalAccountSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    account_officer_id = serializers.UUIDField()

    def validate(self, attrs):
        account_officer_id = attrs.get("account_officer_id")
        try:
            AccountOfficer.objects.get(id=account_officer_id)
        except AccountOfficer.DoesNotExist:
            raise InvalidRequestException({"detail": "Account officer not found"})
        return attrs

    def create(self, validated_data):
        user = validated_data.get("user")
        account_officer_id = validated_data.get("account_officer_id")

        company = user.customer.company

        account_officer_code = AccountOfficer.objects.get(id=account_officer_id).code

        # Find Account Opening Request
        account_opening_request = CompanyCreationRequest.objects.filter(business_email__iexact=company.email).last()
        if not account_opening_request:
            raise InvalidRequestException({"detail": "Account opening request not found"})

        director = json.loads(account_opening_request.directors)[0]

        company_other_name = str(director.get("first_name")) + " " + str(director.get("other_name"))
        company_last_name = director.get("last_name")
        gender = "0" if director.get("gender") == "male" else "1"
        company_date_of_birth = str(director.get("date_of_birth"))
        company_place_of_birth = director.get("state_of_origin")
        company_gender = gender

        # Create Account for Company
        account_creation_response = client.create_account_quick(
            transaction_tracking_ref=generate_transaction_reference(), account_opening_tracking_ref=generate_transaction_reference(),
            customer_id=company.bank_customer_id, last_name=company_last_name, other_names=company_other_name, account_name=str(company.name).upper(),
            phone_no=company.phone_number, gender=company_gender, place_of_birth=company_place_of_birth, date_of_birth=company_date_of_birth,
            address=company.address, account_officer_code=account_officer_code, email=company.email, bvn=company.company_bvn
        )
        if "IsSuccessful" in account_creation_response and account_creation_response.get("IsSuccessful") is True \
                and account_creation_response.get("Message"):
            response_message = account_creation_response.get("Message")
            account_no = response_message.get("AccountNumber")
            bank_one_account_no = response_message.get("BankoneAccountNumber")
            account = CompanyAccount.objects.create(
                company=company, account_number=account_no, bank_one_account_number=bank_one_account_no, account_officer_id=account_officer_id
            )
        else:
            raise InvalidRequestException({"detail": "Account creation failed"})

        # # Notify all company customers about the new account
        # try:
        #     company_customers = Customer.objects.filter(company=company, active=True)
        #     account_data = CompanyAccountSerializerOut(account).data
        #
        #     for customer in company_customers:
        #         # Send SMS notification
        #         sms_message = f"New account created for {company.name}. Account Number: {account_no}. You can now use this account for transactions."
        #
        #         send_notification_async(
        #             notification_type='sms',
        #             recipient=customer.phone_number,
        #             message=sms_message
        #         )
        #
        #         # Send email notification with HTML formatting
        #         email_message = f"""
        #         <html>
        #         <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        #             <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        #                 <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        #                     <h2 style="color: #2c5aa0; margin-bottom: 20px;">New Account Created</h2>
        #
        #                     <p>Dear <strong>{customer.user.get_full_name()}</strong>,</p>
        #
        #                     <p>A new account has been created for your company: <strong>{company.name}</strong></p>
        #
        #                     <div style="background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;">
        #                         <h3 style="color: #155724; margin-top: 0;">Account Details</h3>
        #                         <table style="width: 100%; border-collapse: collapse;">
        #                             <tr>
        #                                 <td style="padding: 8px 0; font-weight: bold; width: 40%;">Account Number:</td>
        #                                 <td style="padding: 8px 0; color: #2c5aa0; font-weight: bold;">{account_no}</td>
        #                             </tr>
        #                             <tr>
        #                                 <td style="padding: 8px 0; font-weight: bold;">Bank One Account Number:</td>
        #                                 <td style="padding: 8px 0;">{bank_one_account_no}</td>
        #                             </tr>
        #                             <tr>
        #                                 <td style="padding: 8px 0; font-weight: bold;">Account Officer:</td>
        #                                 <td style="padding: 8px 0;">{AccountOfficer.objects.get(id=account_officer_id).name}</td>
        #                             </tr>
        #                         </table>
        #                     </div>
        #
        #                     <p>You can now use this account for your business transactions.</p>
        #
        #                     <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">
        #
        #                     <p style="color: #666; font-size: 14px;">
        #                         Best regards,<br>
        #                         <strong>Bowen MFB Team</strong>
        #                     </p>
        #                 </div>
        #             </div>
        #         </body>
        #         </html>
        #         """
        #
        #         send_notification_async(
        #             notification_type='email',
        #             recipient=customer.user.email,
        #             message=email_message,
        #             subject=f'New Account Created - {company.name}'
        #         )
        #
        # except Exception as e:
        #     log_request(f"Failed to send new account notifications for company {company.name}: {str(e)}")

        return {"detail": "New account created successfully", "data": CompanyAccountSerializerOut(account).data}


class ChangeTransactionPinSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    old_pin = serializers.CharField(max_length=4)
    new_pin = serializers.CharField(max_length=4)
    otp = serializers.CharField(max_length=6)

    def validate(self, attrs):
        user = attrs.get("user")
        old_pin = attrs.get("old_pin")
        new_pin = attrs.get("new_pin")
        otp = attrs.get("otp")
        if not (str(otp).isnumeric() and len(otp) == 6):
            raise InvalidRequestException({"detail": "Invalid OTP"})
        if not (str(old_pin).isnumeric() and len(old_pin) == 4):
            raise InvalidRequestException({"detail": "PIN must be four (4) digits"})

        if not (str(new_pin).isnumeric() and len(new_pin) == 4):
            raise InvalidRequestException({"detail": "PIN must be four (4) digits"})

        if old_pin == new_pin:
            raise InvalidRequestException({"detail": "PINs cannot be the same"})

        customer = user.customer
        if customer.failed_pin_retries >= 5:
            raise MaximumApprovalPINRetriesException()
        if otp != customer.get_decrypted_verification_token:
            raise InvalidRequestException({"detail": "Incorrect OTP"})
        if timezone.now() > customer.verification_token_expiry:
            raise InvalidRequestException({"detail": "OTP has expired"})

        decrypted_pin = str(customer.get_decrypted_approval_pin)
        if decrypted_pin != old_pin:
            customer.failed_pin_retries += 1
            customer.save()
            raise InvalidRequestException({"detail": "Incorrect PIN"})

        return attrs

    def create(self, validated_data):
        user = validated_data.get("user")
        new_pin = validated_data.get("new_pin")

        customer = user.customer
        customer.approval_pin = encrypt_text(new_pin)
        customer.failed_pin_retries = 0
        customer.save()

        return {"detail": "Transaction PIN changed successfully"}


class ResetTransactionPinSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    new_pin = serializers.CharField(max_length=4)
    otp = serializers.CharField(max_length=6)

    def validate(self, attrs):
        user = attrs.get("user")
        new_pin = attrs.get("new_pin")
        otp = attrs.get("otp")

        if not (str(otp).isnumeric() and len(otp) == 6):
            raise InvalidRequestException({"detail": "Invalid OTP"})

        if not (str(new_pin).isnumeric() and len(new_pin) == 4):
            raise InvalidRequestException({"detail": "PIN must be four (4) digits"})

        customer = user.customer

        if customer.failed_pin_retries >= 5:
            raise MaximumApprovalPINRetriesException()

        if otp != customer.get_decrypted_verification_token:
            raise InvalidRequestException({"detail": "Incorrect OTP"})

        if timezone.now() > customer.verification_token_expiry:
            raise InvalidRequestException({"detail": "OTP has expired"})

        if customer.last_pin_reset is not None and (timezone.now() - customer.last_pin_reset) < timedelta(days=30):
            raise InvalidRequestException({"detail": "You can only reset your PIN once every 30 days"})

        return attrs

    def create(self, validated_data):
        user = validated_data.get("user")
        new_pin = validated_data.get("new_pin")
        customer = user.customer
        customer.approval_pin = encrypt_text(new_pin)
        customer.last_pin_reset = timezone.now()
        customer.save()
        return {"detail": "Transaction PIN reset successfully"}


class UpdateProfileImageSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    image = serializers.CharField()

    def validate(self, attrs):
        image = attrs.get("image")

        # Check if it starts with a valid base64 image prefix
        pattern = r'^data:image\/[a-zA-Z]+;base64,'
        try:
            re.match(pattern, image)
            # Try decoding the Base64 part
            base64_str = image.split(';base64,')[1]
            base64.b64decode(base64_str, validate=True)
        except Exception as err:
            log_request(f"Failed to decode image: {str(err)}")
            raise InvalidRequestException({"detail": "A valid image format is required"})

        return attrs

    def create(self, validated_data):
        user = validated_data.get("user")
        image = validated_data.get("image")
        user.customer.image = image
        user.customer.save()
        return {"detail": "Profile image updated successfully"}


class InAppNotificationSerializerOut(serializers.ModelSerializer):
    company_id = serializers.UUIDField(source='company.id', read_only=True)

    class Meta:
        model = InAppNotification
        exclude = ["company", "related_object_id", "related_object_type"]


class ExistingCustomerAccountSerializerIn(serializers.Serializer):
    bvn = serializers.CharField()
    account_number = serializers.CharField()
    customer_id = serializers.CharField()

    def validate(self, attrs):
        bvn = attrs.get("bvn")
        account_number = attrs.get("account_number")
        customer_id = attrs.get("customer_id")

        # Check if BVN is valid
        if not (str(bvn).isnumeric() and len(bvn) == 11):
            raise InvalidRequestException({"detail": "BVN is not valid"})
        # Check if account number is valid
        if not (str(account_number).isnumeric() and len(account_number) == 10):
            raise InvalidRequestException({"detail": "Account number is not valid"})
        # Check if company already exists
        if Company.objects.filter(bank_customer_id=customer_id).exists():
            raise InvalidRequestException({"detail": "Company already exists"})
        # Check if account already exists
        if CompanyAccount.objects.filter(account_number=account_number).exists():
            raise InvalidRequestException({"detail": "Account already exists"})
        # Check if a company with BVN exists
        if Company.objects.filter(company_bvn=bvn).exists():
            raise InvalidRequestException({"detail": "Company with BVN already exists"})

        return attrs

    def create(self, validated_data):
        customer_id = validated_data.get("customer_id")

        # Check if the customer exists on bankone
        try:
            response = client.get_customer_by_customer_id(customer_id=customer_id)
            if "IsSuccessful" in response and response.get("IsSuccessful") is False:
                raise InvalidRequestException({"detail": "Account not found or invalid customer ID"})
            elif "CustomerId" in response and response.get("CustomerId") == customer_id:
                # Get data to create company and company account
                name = response.get("BusinessName") if response.get("BusinessName") else response.get("Name")
                email = response.get("Email")
                phone_number = response.get("PhoneNo") if response.get("PhoneNo") else response.get("BusinessPhoneNo")
                address = response.get("Address") if response.get("Address") else response.get("PostalAddress")
                website = response.get("WebAddress")
                tax_number = response.get("TaxIDNo")
                registration_number = response.get("RegistrationNumber")
                registration_date = response.get("CompanyRegDate") if response.get("CompanyRegDate") else response.get("BusinessCommencementDate")

                # Create company
                company = Company.objects.create(
                    name=name, bank_customer_id=customer_id, active=True, address=address, email=email, phone_number=phone_number,
                    website=website, tax_number=tax_number, registration_number=registration_number, registration_date=registration_date,
                )

                accounts = response.get("Accounts")
                for account in accounts:
                    account_number = account.get("AccountNumber")
                    # Create company account
                    CompanyAccount.objects.create(company=company, account_number=account_number)

                return {"detail": "Account created successfully"}
            else:
                raise InvalidRequestException({"detail": "Account not found or invalid customer ID, please try again"})
        except Exception as err:
            log_request(f"Failed to create account: {str(err)}")
            raise InvalidRequestException({"detail": "Failed to create account, please try again later"})


